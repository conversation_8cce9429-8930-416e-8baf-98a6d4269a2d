{"_name_or_path": "training/model_zoo/LLM/gemma/gemma-2b-it", "architectures": ["GemmaForCausalLM"], "attention_bias": false, "attention_dropout": 0.0, "bos_token_id": 2, "eos_token_id": 1, "freeze_mm_mlp_adapter": false, "head_dim": 256, "hidden_act": "gelu", "hidden_size": 2048, "image_aspect_ratio": "pad", "image_global": false, "image_grid": 1, "image_grid_pinpoints": null, "image_size_aux": 768, "initializer_range": 0.02, "intermediate_size": 16384, "max_position_embeddings": 8192, "mm_hidden_size": 1024, "mm_hidden_size_aux": 2880, "mm_projector_lr": null, "mm_projector_type": "mlp2x_gelu", "mm_use_im_patch_token": false, "mm_use_im_start_end": false, "mm_vision_select_feature": "patch", "mm_vision_select_layer": -2, "mm_vision_tower": "training/model_zoo/OpenAI/clip-vit-large-patch14-336", "mm_vision_tower_aux": "training/model_zoo/OpenAI/openclip-convnext-large-d-320-laion2B-s29B-b131K-ft-soup", "model_type": "mgm_gemma", "num_attention_heads": 8, "num_hidden_layers": 18, "num_key_value_heads": 1, "optimize_vision_tower": false, "optimize_vision_tower_aux": false, "pad_token_id": 0, "rms_norm_eps": 1e-06, "rope_scaling": null, "rope_theta": 10000.0, "tokenizer_model_max_length": 1024, "tokenizer_padding_side": "right", "torch_dtype": "bfloat16", "transformers_version": "4.38.0", "tune_mm_mlp_adapter": false, "use_cache": true, "use_mm_proj": true, "vocab_size": 256000}