{"text": "2025-07-01 16:41:51.783 | DEBUG    | data_juicer.config.config:35 - Initializing setup from config took 0.01 seconds\n", "record": {"elapsed": {"repr": "0:00:01.982075", "seconds": 1.982075}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Initializing setup from config took 0.01 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:51.783196+08:00", "timestamp": 1751359311.783196}}}
{"text": "2025-07-01 16:41:51.806 | DEBUG    | data_juicer.config.config:35 - Updating operator process took 0.02 seconds\n", "record": {"elapsed": {"repr": "0:00:02.005192", "seconds": 2.005192}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Updating operator process took 0.02 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:51.806313+08:00", "timestamp": 1751359311.806313}}}
{"text": "2025-07-01 16:41:51.811 | DEBUG    | data_juicer.config.config:35 - Total config initialization time took 0.17 seconds\n", "record": {"elapsed": {"repr": "0:00:02.010236", "seconds": 2.010236}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Total config initialization time took 0.17 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:51.811357+08:00", "timestamp": 1751359311.811357}}}
{"text": "2025-07-01 16:41:53.179 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'fork'\n", "record": {"elapsed": {"repr": "0:00:03.378865", "seconds": 3.378865}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'fork'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:53.179986+08:00", "timestamp": 1751359313.179986}}}
{"text": "2025-07-01 16:41:53.212 | DEBUG    | data_juicer.ops.base_op:216 - Op [text_length_filter] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:03.411225", "seconds": 3.411225}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [text_length_filter] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:53.212346+08:00", "timestamp": 1751359313.212346}}}
{"text": "2025-07-01 16:41:53.386 | DEBUG    | data_juicer.ops.base_op:216 - Op [text_length_filter] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:03.585104", "seconds": 3.585104}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [text_length_filter] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:53.386225+08:00", "timestamp": 1751359313.386225}}}
{"text": "2025-07-01 16:41:53.545 | DEBUG    | data_juicer.ops.base_op:216 - Op [text_length_filter] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:03.743952", "seconds": 3.743952}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [text_length_filter] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:53.545073+08:00", "timestamp": 1751359313.545073}}}
{"text": "2025-07-01 16:41:53.703 | DEBUG    | data_juicer.utils.lazy_loader:466 - Loading torch...\n", "record": {"elapsed": {"repr": "0:00:03.902059", "seconds": 3.902059}, "exception": null, "extra": {}, "file": {"name": "lazy_loader.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/lazy_loader.py"}, "function": "_load", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 466, "message": "Loading torch...", "module": "lazy_loader", "name": "data_juicer.utils.lazy_loader", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:53.703180+08:00", "timestamp": 1751359313.70318}}}
{"text": "2025-07-01 16:41:53.799 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'fork'\n", "record": {"elapsed": {"repr": "0:00:03.998705", "seconds": 3.998705}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'fork'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:53.799826+08:00", "timestamp": 1751359313.799826}}}
{"text": "2025-07-01 16:41:53.844 | DEBUG    | data_juicer.ops.base_op:216 - Op [image_shape_filter] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:04.043797", "seconds": 4.043797}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [image_shape_filter] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:53.844918+08:00", "timestamp": 1751359313.844918}}}
{"text": "2025-07-01 16:41:55.531 | DEBUG    | data_juicer.ops.base_op:216 - Op [image_shape_filter] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:05.730678", "seconds": 5.730678}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [image_shape_filter] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:55.531799+08:00", "timestamp": 1751359315.531799}}}
{"text": "2025-07-01 16:41:56.099 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.298099", "seconds": 6.298099}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:56.099220+08:00", "timestamp": 1751359316.09922}}}
{"text": "2025-07-01 16:41:56.099 | DEBUG    | data_juicer.utils.lazy_loader:466 - Loading torch...\n", "record": {"elapsed": {"repr": "0:00:06.298267", "seconds": 6.298267}, "exception": null, "extra": {}, "file": {"name": "lazy_loader.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/lazy_loader.py"}, "function": "_load", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 466, "message": "Loading torch...", "module": "lazy_loader", "name": "data_juicer.utils.lazy_loader", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:56.099388+08:00", "timestamp": 1751359316.099388}}}
{"text": "2025-07-01 16:41:56.100 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'forkserver'\n", "record": {"elapsed": {"repr": "0:00:06.298999", "seconds": 6.298999}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'forkserver'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:56.100120+08:00", "timestamp": 1751359316.10012}}}
{"text": "2025-07-01 16:41:56.145 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.344145", "seconds": 6.344145}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:56.145266+08:00", "timestamp": 1751359316.145266}}}
{"text": "2025-07-01 16:41:56.178 | DEBUG    | data_juicer.ops.base_op:216 - Op [image_captioning_mapper] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:06.377368", "seconds": 6.377368}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [image_captioning_mapper] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:56.178489+08:00", "timestamp": 1751359316.178489}}}
{"text": "2025-07-01 16:41:56.179 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.378516", "seconds": 6.378516}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:56.179637+08:00", "timestamp": 1751359316.179637}}}
{"text": "2025-07-01 16:41:56.180 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.379425", "seconds": 6.379425}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:56.180546+08:00", "timestamp": 1751359316.180546}}}
{"text": "2025-07-01 16:42:10.761 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:20.960837", "seconds": 20.960837}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:42:10.761958+08:00", "timestamp": 1751359330.761958}}}
{"text": "2025-07-01 16:42:10.762 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'forkserver'\n", "record": {"elapsed": {"repr": "0:00:20.960985", "seconds": 20.960985}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'forkserver'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:42:10.762106+08:00", "timestamp": 1751359330.762106}}}
{"text": "2025-07-01 16:42:10.778 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:20.977750", "seconds": 20.97775}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:42:10.778871+08:00", "timestamp": 1751359330.778871}}}
{"text": "2025-07-01 16:42:10.793 | DEBUG    | data_juicer.ops.base_op:216 - Op [image_text_similarity_filter] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:20.992085", "seconds": 20.992085}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [image_text_similarity_filter] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:42:10.793206+08:00", "timestamp": 1751359330.793206}}}
{"text": "2025-07-01 16:42:10.794 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:20.993591", "seconds": 20.993591}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:42:10.794712+08:00", "timestamp": 1751359330.794712}}}
{"text": "2025-07-01 16:42:10.796 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:20.994936", "seconds": 20.994936}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:42:10.796057+08:00", "timestamp": 1751359330.796057}}}
{"text": "2025-07-01 16:42:10.800 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:20.998989", "seconds": 20.998989}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:42:10.800110+08:00", "timestamp": 1751359330.80011}}}
{"text": "2025-07-01 16:42:10.816 | DEBUG    | data_juicer.ops.base_op:216 - Op [image_text_similarity_filter] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:21.015090", "seconds": 21.01509}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [image_text_similarity_filter] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:42:10.816211+08:00", "timestamp": 1751359330.816211}}}
