{"text": "2025-07-01 16:41:56.177 | WARNING  | data_juicer.utils.process_utils:64 - The required cuda memory of Op[image_captioning_mapper] has not been specified. Please specify the mem_required field in the config file, or you might encounter CUDA out of memory error. You can reference the mem_required field in the config_all.yaml file.\n", "record": {"elapsed": {"repr": "0:00:06.376734", "seconds": 6.376734}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "calculate_np", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 64, "message": "The required cuda memory of Op[image_captioning_mapper] has not been specified. Please specify the mem_required field in the config file, or you might encounter CUDA out of memory error. You can reference the mem_required field in the config_all.yaml file.", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:41:56.177855+08:00", "timestamp": 1751359316.177855}}}
{"text": "2025-07-01 16:42:10.792 | WARNING  | data_juicer.utils.process_utils:64 - The required cuda memory of Op[image_text_similarity_filter] has not been specified. Please specify the mem_required field in the config file, or you might encounter CUDA out of memory error. You can reference the mem_required field in the config_all.yaml file.\n", "record": {"elapsed": {"repr": "0:00:20.991327", "seconds": 20.991327}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "calculate_np", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 64, "message": "The required cuda memory of Op[image_text_similarity_filter] has not been specified. Please specify the mem_required field in the config file, or you might encounter CUDA out of memory error. You can reference the mem_required field in the config_all.yaml file.", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:42:10.792448+08:00", "timestamp": 1751359330.792448}}}
{"text": "2025-07-01 16:42:10.815 | WARNING  | data_juicer.utils.process_utils:64 - The required cuda memory of Op[image_text_similarity_filter] has not been specified. Please specify the mem_required field in the config file, or you might encounter CUDA out of memory error. You can reference the mem_required field in the config_all.yaml file.\n", "record": {"elapsed": {"repr": "0:00:21.014614", "seconds": 21.014614}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "calculate_np", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 64, "message": "The required cuda memory of Op[image_text_similarity_filter] has not been specified. Please specify the mem_required field in the config file, or you might encounter CUDA out of memory error. You can reference the mem_required field in the config_all.yaml file.", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 17823, "name": "MainProcess"}, "thread": {"id": 137413302117440, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:42:10.815735+08:00", "timestamp": 1751359330.815735}}}
