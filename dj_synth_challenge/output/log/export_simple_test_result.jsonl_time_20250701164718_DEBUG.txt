{"text": "2025-07-01 16:47:18.184 | DEBUG    | data_juicer.config.config:35 - Initializing setup from config took 0.01 seconds\n", "record": {"elapsed": {"repr": "0:00:01.856681", "seconds": 1.856681}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Initializing setup from config took 0.01 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 19487, "name": "MainProcess"}, "thread": {"id": 139978426229824, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:47:18.184985+08:00", "timestamp": 1751359638.184985}}}
{"text": "2025-07-01 16:47:18.197 | DEBUG    | data_juicer.config.config:35 - Updating operator process took 0.01 seconds\n", "record": {"elapsed": {"repr": "0:00:01.869197", "seconds": 1.869197}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Updating operator process took 0.01 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 19487, "name": "MainProcess"}, "thread": {"id": 139978426229824, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:47:18.197501+08:00", "timestamp": 1751359638.197501}}}
{"text": "2025-07-01 16:47:18.201 | DEBUG    | data_juicer.config.config:35 - Total config initialization time took 0.12 seconds\n", "record": {"elapsed": {"repr": "0:00:01.873005", "seconds": 1.873005}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Total config initialization time took 0.12 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 19487, "name": "MainProcess"}, "thread": {"id": 139978426229824, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:47:18.201309+08:00", "timestamp": 1751359638.201309}}}
{"text": "2025-07-01 16:47:19.135 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'fork'\n", "record": {"elapsed": {"repr": "0:00:02.807469", "seconds": 2.807469}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'fork'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 19487, "name": "MainProcess"}, "thread": {"id": 139978426229824, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:47:19.135773+08:00", "timestamp": 1751359639.135773}}}
{"text": "2025-07-01 16:47:19.159 | DEBUG    | data_juicer.ops.base_op:216 - Op [text_length_filter] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:02.830831", "seconds": 2.830831}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [text_length_filter] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 19487, "name": "MainProcess"}, "thread": {"id": 139978426229824, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:47:19.159135+08:00", "timestamp": 1751359639.159135}}}
{"text": "2025-07-01 16:47:19.168 | DEBUG    | data_juicer.ops.base_op:216 - Op [text_length_filter] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:02.840631", "seconds": 2.840631}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [text_length_filter] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 19487, "name": "MainProcess"}, "thread": {"id": 139978426229824, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:47:19.168935+08:00", "timestamp": 1751359639.168935}}}
{"text": "2025-07-01 16:47:19.312 | DEBUG    | data_juicer.ops.base_op:216 - Op [text_length_filter] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:02.983705", "seconds": 2.983705}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [text_length_filter] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 19487, "name": "MainProcess"}, "thread": {"id": 139978426229824, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:47:19.312009+08:00", "timestamp": 1751359639.312009}}}
{"text": "2025-07-01 16:47:19.473 | DEBUG    | data_juicer.utils.lazy_loader:466 - Loading torch...\n", "record": {"elapsed": {"repr": "0:00:03.145495", "seconds": 3.145495}, "exception": null, "extra": {}, "file": {"name": "lazy_loader.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/lazy_loader.py"}, "function": "_load", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 466, "message": "Loading torch...", "module": "lazy_loader", "name": "data_juicer.utils.lazy_loader", "process": {"id": 19487, "name": "MainProcess"}, "thread": {"id": 139978426229824, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:47:19.473799+08:00", "timestamp": 1751359639.473799}}}
{"text": "2025-07-01 16:47:19.726 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'fork'\n", "record": {"elapsed": {"repr": "0:00:03.397953", "seconds": 3.397953}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'fork'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 19487, "name": "MainProcess"}, "thread": {"id": 139978426229824, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:47:19.726257+08:00", "timestamp": 1751359639.726257}}}
{"text": "2025-07-01 16:47:19.741 | DEBUG    | data_juicer.ops.base_op:216 - Op [image_shape_filter] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:03.413422", "seconds": 3.413422}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [image_shape_filter] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 19487, "name": "MainProcess"}, "thread": {"id": 139978426229824, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:47:19.741726+08:00", "timestamp": 1751359639.741726}}}
{"text": "2025-07-01 16:47:21.269 | DEBUG    | data_juicer.ops.base_op:216 - Op [image_shape_filter] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:04.940760", "seconds": 4.94076}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [image_shape_filter] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 19487, "name": "MainProcess"}, "thread": {"id": 139978426229824, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:47:21.269064+08:00", "timestamp": 1751359641.269064}}}
