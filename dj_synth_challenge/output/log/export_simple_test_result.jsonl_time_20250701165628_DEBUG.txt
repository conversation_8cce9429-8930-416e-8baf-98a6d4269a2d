{"text": "2025-07-01 16:56:28.456 | DEBUG    | data_juicer.config.config:35 - Initializing setup from config took 0.01 seconds\n", "record": {"elapsed": {"repr": "0:00:01.947305", "seconds": 1.947305}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Initializing setup from config took 0.01 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 21314, "name": "MainProcess"}, "thread": {"id": 130786456863808, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:56:28.456133+08:00", "timestamp": 1751360188.456133}}}
{"text": "2025-07-01 16:56:28.468 | DEBUG    | data_juicer.config.config:35 - Updating operator process took 0.01 seconds\n", "record": {"elapsed": {"repr": "0:00:01.959951", "seconds": 1.959951}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Updating operator process took 0.01 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 21314, "name": "MainProcess"}, "thread": {"id": 130786456863808, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:56:28.468779+08:00", "timestamp": 1751360188.468779}}}
{"text": "2025-07-01 16:56:28.472 | DEBUG    | data_juicer.config.config:35 - Total config initialization time took 0.12 seconds\n", "record": {"elapsed": {"repr": "0:00:01.963719", "seconds": 1.963719}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Total config initialization time took 0.12 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 21314, "name": "MainProcess"}, "thread": {"id": 130786456863808, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:56:28.472547+08:00", "timestamp": 1751360188.472547}}}
{"text": "2025-07-01 16:56:29.276 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'fork'\n", "record": {"elapsed": {"repr": "0:00:02.768009", "seconds": 2.768009}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'fork'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 21314, "name": "MainProcess"}, "thread": {"id": 130786456863808, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:56:29.276837+08:00", "timestamp": 1751360189.276837}}}
{"text": "2025-07-01 16:56:29.299 | DEBUG    | data_juicer.ops.base_op:216 - Op [text_length_filter] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:02.791035", "seconds": 2.791035}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [text_length_filter] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 21314, "name": "MainProcess"}, "thread": {"id": 130786456863808, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:56:29.299863+08:00", "timestamp": 1751360189.299863}}}
{"text": "2025-07-01 16:56:29.307 | DEBUG    | data_juicer.ops.base_op:216 - Op [text_length_filter] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:02.798653", "seconds": 2.798653}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [text_length_filter] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 21314, "name": "MainProcess"}, "thread": {"id": 130786456863808, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:56:29.307481+08:00", "timestamp": 1751360189.307481}}}
{"text": "2025-07-01 16:56:29.315 | DEBUG    | data_juicer.ops.base_op:216 - Op [text_length_filter] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:02.807121", "seconds": 2.807121}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [text_length_filter] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 21314, "name": "MainProcess"}, "thread": {"id": 130786456863808, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:56:29.315949+08:00", "timestamp": 1751360189.315949}}}
{"text": "2025-07-01 16:56:29.322 | DEBUG    | data_juicer.utils.lazy_loader:466 - Loading torch...\n", "record": {"elapsed": {"repr": "0:00:02.813521", "seconds": 2.813521}, "exception": null, "extra": {}, "file": {"name": "lazy_loader.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/lazy_loader.py"}, "function": "_load", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 466, "message": "Loading torch...", "module": "lazy_loader", "name": "data_juicer.utils.lazy_loader", "process": {"id": 21314, "name": "MainProcess"}, "thread": {"id": 130786456863808, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:56:29.322349+08:00", "timestamp": 1751360189.322349}}}
{"text": "2025-07-01 16:56:29.881 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'fork'\n", "record": {"elapsed": {"repr": "0:00:03.373155", "seconds": 3.373155}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'fork'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 21314, "name": "MainProcess"}, "thread": {"id": 130786456863808, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:56:29.881983+08:00", "timestamp": 1751360189.881983}}}
{"text": "2025-07-01 16:56:29.912 | DEBUG    | data_juicer.ops.base_op:216 - Op [image_shape_filter] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:03.403258", "seconds": 3.403258}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [image_shape_filter] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 21314, "name": "MainProcess"}, "thread": {"id": 130786456863808, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:56:29.912086+08:00", "timestamp": 1751360189.912086}}}
{"text": "2025-07-01 16:56:29.925 | DEBUG    | data_juicer.ops.base_op:216 - Op [image_shape_filter] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:03.416691", "seconds": 3.416691}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [image_shape_filter] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 21314, "name": "MainProcess"}, "thread": {"id": 130786456863808, "name": "MainThread"}, "time": {"repr": "2025-07-01 16:56:29.925519+08:00", "timestamp": 1751360189.925519}}}
