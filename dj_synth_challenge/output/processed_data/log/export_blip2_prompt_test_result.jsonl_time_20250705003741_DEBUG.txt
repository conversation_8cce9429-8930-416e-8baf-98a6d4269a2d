{"text": "2025-07-05 00:37:41.397 | DEBUG    | data_juicer.config.config:35 - Initializing setup from config took 0.01 seconds\n", "record": {"elapsed": {"repr": "0:00:01.901679", "seconds": 1.901679}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Initializing setup from config took 0.01 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:41.397382+08:00", "timestamp": 1751647061.397382}}}
{"text": "2025-07-05 00:37:41.415 | DEBUG    | data_juicer.config.config:35 - Updating operator process took 0.02 seconds\n", "record": {"elapsed": {"repr": "0:00:01.919988", "seconds": 1.919988}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Updating operator process took 0.02 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:41.415691+08:00", "timestamp": 1751647061.415691}}}
{"text": "2025-07-05 00:37:41.420 | DEBUG    | data_juicer.config.config:35 - Total config initialization time took 0.08 seconds\n", "record": {"elapsed": {"repr": "0:00:01.924942", "seconds": 1.924942}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Total config initialization time took 0.08 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:41.420645+08:00", "timestamp": 1751647061.420645}}}
{"text": "2025-07-05 00:37:42.288 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:02.792331", "seconds": 2.792331}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:42.288034+08:00", "timestamp": 1751647062.288034}}}
{"text": "2025-07-05 00:37:42.288 | DEBUG    | data_juicer.utils.lazy_loader:466 - Loading torch...\n", "record": {"elapsed": {"repr": "0:00:02.792427", "seconds": 2.792427}, "exception": null, "extra": {}, "file": {"name": "lazy_loader.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/lazy_loader.py"}, "function": "_load", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 466, "message": "Loading torch...", "module": "lazy_loader", "name": "data_juicer.utils.lazy_loader", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:42.288130+08:00", "timestamp": 1751647062.28813}}}
{"text": "2025-07-05 00:37:42.288 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'forkserver'\n", "record": {"elapsed": {"repr": "0:00:02.792709", "seconds": 2.792709}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'forkserver'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:42.288412+08:00", "timestamp": 1751647062.288412}}}
{"text": "2025-07-05 00:37:42.308 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:02.812357", "seconds": 2.812357}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:42.308060+08:00", "timestamp": 1751647062.30806}}}
{"text": "2025-07-05 00:37:42.322 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:02.826887", "seconds": 2.826887}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:42.322590+08:00", "timestamp": 1751647062.32259}}}
{"text": "2025-07-05 00:37:42.322 | DEBUG    | data_juicer.ops.base_op:216 - Op [image_captioning_mapper] running with number of procs:1\n", "record": {"elapsed": {"repr": "0:00:02.827228", "seconds": 2.827228}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [image_captioning_mapper] running with number of procs:1", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:42.322931+08:00", "timestamp": 1751647062.322931}}}
{"text": "2025-07-05 00:37:42.323 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:02.828292", "seconds": 2.828292}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:42.323995+08:00", "timestamp": 1751647062.323995}}}
{"text": "2025-07-05 00:37:42.325 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:02.829376", "seconds": 2.829376}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:42.325079+08:00", "timestamp": 1751647062.325079}}}
{"text": "2025-07-05 00:37:42.334 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:02.838858", "seconds": 2.838858}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:42.334561+08:00", "timestamp": 1751647062.334561}}}
{"text": "2025-07-05 00:37:42.334 | DEBUG    | data_juicer.utils.model_utils:1110 - functools.partial(<function prepare_huggingface_model at 0x74c04c14a710>, pretrained_model_name_or_path='/home/<USER>/.cache/modelscope/hub/models/goldsj/blip2-opt-2.7b', trust_remote_code=False) not found in MODEL_ZOO (MainProcess)\n", "record": {"elapsed": {"repr": "0:00:02.839001", "seconds": 2.839001}, "exception": null, "extra": {}, "file": {"name": "model_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/model_utils.py"}, "function": "get_model", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 1110, "message": "functools.partial(<function prepare_huggingface_model at 0x74c04c14a710>, pretrained_model_name_or_path='/home/<USER>/.cache/modelscope/hub/models/goldsj/blip2-opt-2.7b', trust_remote_code=False) not found in MODEL_ZOO (MainProcess)", "module": "model_utils", "name": "data_juicer.utils.model_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:42.334704+08:00", "timestamp": 1751647062.334704}}}
{"text": "2025-07-05 00:37:42.335 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:02.839558", "seconds": 2.839558}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:42.335261+08:00", "timestamp": 1751647062.335261}}}
{"text": "2025-07-05 00:37:42.335 | DEBUG    | data_juicer.utils.lazy_loader:466 - Loading transformers...\n", "record": {"elapsed": {"repr": "0:00:02.839686", "seconds": 2.839686}, "exception": null, "extra": {}, "file": {"name": "lazy_loader.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/lazy_loader.py"}, "function": "_load", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 466, "message": "Loading transformers...", "module": "lazy_loader", "name": "data_juicer.utils.lazy_loader", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:42.335389+08:00", "timestamp": 1751647062.335389}}}
{"text": "2025-07-05 00:37:45.518 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.022877", "seconds": 6.022877}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:45.518580+08:00", "timestamp": 1751647065.51858}}}
{"text": "2025-07-05 00:37:45.578 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.082561", "seconds": 6.082561}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:45.578264+08:00", "timestamp": 1751647065.578264}}}
{"text": "2025-07-05 00:37:45.638 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.142586", "seconds": 6.142586}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:45.638289+08:00", "timestamp": 1751647065.638289}}}
{"text": "2025-07-05 00:37:46.261 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.765455", "seconds": 6.765455}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:46.261158+08:00", "timestamp": 1751647066.261158}}}
{"text": "2025-07-05 00:37:46.321 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.825491", "seconds": 6.825491}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:46.321194+08:00", "timestamp": 1751647066.321194}}}
{"text": "2025-07-05 00:37:46.380 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.884410", "seconds": 6.88441}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:46.380113+08:00", "timestamp": 1751647066.380113}}}
{"text": "2025-07-05 00:37:46.440 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.944699", "seconds": 6.944699}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:46.440402+08:00", "timestamp": 1751647066.440402}}}
{"text": "2025-07-05 00:37:46.675 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.180265", "seconds": 7.180265}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:46.675968+08:00", "timestamp": 1751647066.675968}}}
{"text": "2025-07-05 00:37:47.562 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:08.067073", "seconds": 8.067073}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:47.562776+08:00", "timestamp": 1751647067.562776}}}
{"text": "2025-07-05 00:37:47.812 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:08.317211", "seconds": 8.317211}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:47.812914+08:00", "timestamp": 1751647067.812914}}}
{"text": "2025-07-05 00:37:48.446 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:08.950897", "seconds": 8.950897}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:48.446600+08:00", "timestamp": 1751647068.4466}}}
{"text": "2025-07-05 00:37:48.506 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.010599", "seconds": 9.010599}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:48.506302+08:00", "timestamp": 1751647068.506302}}}
{"text": "2025-07-05 00:37:49.227 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.731779", "seconds": 9.731779}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:49.227482+08:00", "timestamp": 1751647069.227482}}}
{"text": "2025-07-05 00:37:49.287 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.791622", "seconds": 9.791622}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:49.287325+08:00", "timestamp": 1751647069.287325}}}
{"text": "2025-07-05 00:37:49.346 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.850998", "seconds": 9.850998}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:49.346701+08:00", "timestamp": 1751647069.346701}}}
{"text": "2025-07-05 00:37:49.495 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.999706", "seconds": 9.999706}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:49.495409+08:00", "timestamp": 1751647069.495409}}}
{"text": "2025-07-05 00:37:49.556 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:10.060454", "seconds": 10.060454}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:49.556157+08:00", "timestamp": 1751647069.556157}}}
{"text": "2025-07-05 00:37:49.675 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:10.179328", "seconds": 10.179328}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:49.675031+08:00", "timestamp": 1751647069.675031}}}
{"text": "2025-07-05 00:37:49.778 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:10.282734", "seconds": 10.282734}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:49.778437+08:00", "timestamp": 1751647069.778437}}}
{"text": "2025-07-05 00:37:49.838 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:10.342446", "seconds": 10.342446}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:49.838149+08:00", "timestamp": 1751647069.838149}}}
{"text": "2025-07-05 00:37:51.270 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:11.774616", "seconds": 11.774616}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:51.270319+08:00", "timestamp": 1751647071.270319}}}
{"text": "2025-07-05 00:37:51.330 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:11.834586", "seconds": 11.834586}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:51.330289+08:00", "timestamp": 1751647071.330289}}}
{"text": "2025-07-05 00:37:51.389 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:11.893851", "seconds": 11.893851}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:51.389554+08:00", "timestamp": 1751647071.389554}}}
{"text": "2025-07-05 00:37:51.448 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:11.952760", "seconds": 11.95276}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:51.448463+08:00", "timestamp": 1751647071.448463}}}
{"text": "2025-07-05 00:37:51.508 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.012573", "seconds": 12.012573}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:51.508276+08:00", "timestamp": 1751647071.508276}}}
{"text": "2025-07-05 00:37:51.567 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.071994", "seconds": 12.071994}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:51.567697+08:00", "timestamp": 1751647071.567697}}}
{"text": "2025-07-05 00:37:52.034 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.539050", "seconds": 12.53905}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:52.034753+08:00", "timestamp": 1751647072.034753}}}
{"text": "2025-07-05 00:37:52.387 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.892101", "seconds": 12.892101}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:52.387804+08:00", "timestamp": 1751647072.387804}}}
{"text": "2025-07-05 00:37:52.725 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.229503", "seconds": 13.229503}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:52.725206+08:00", "timestamp": 1751647072.725206}}}
{"text": "2025-07-05 00:37:52.858 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.362679", "seconds": 13.362679}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:52.858382+08:00", "timestamp": 1751647072.858382}}}
{"text": "2025-07-05 00:37:52.917 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.421441", "seconds": 13.421441}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:52.917144+08:00", "timestamp": 1751647072.917144}}}
{"text": "2025-07-05 00:37:53.394 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.898965", "seconds": 13.898965}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:53.394668+08:00", "timestamp": 1751647073.394668}}}
{"text": "2025-07-05 00:37:53.455 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.960161", "seconds": 13.960161}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:53.455864+08:00", "timestamp": 1751647073.455864}}}
{"text": "2025-07-05 00:37:54.926 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:15.430701", "seconds": 15.430701}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:54.926404+08:00", "timestamp": 1751647074.926404}}}
{"text": "2025-07-05 00:37:55.561 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:16.065939", "seconds": 16.065939}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:55.561642+08:00", "timestamp": 1751647075.561642}}}
{"text": "2025-07-05 00:37:56.018 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:16.522740", "seconds": 16.52274}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:56.018443+08:00", "timestamp": 1751647076.018443}}}
{"text": "2025-07-05 00:37:57.959 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:18.464156", "seconds": 18.464156}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:57.959859+08:00", "timestamp": 1751647077.959859}}}
{"text": "2025-07-05 00:37:58.019 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:18.524126", "seconds": 18.524126}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:58.019829+08:00", "timestamp": 1751647078.019829}}}
{"text": "2025-07-05 00:37:58.079 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:18.583791", "seconds": 18.583791}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:58.079494+08:00", "timestamp": 1751647078.079494}}}
{"text": "2025-07-05 00:37:58.580 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:19.085288", "seconds": 19.085288}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:58.580991+08:00", "timestamp": 1751647078.580991}}}
{"text": "2025-07-05 00:37:58.640 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:19.144807", "seconds": 19.144807}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:58.640510+08:00", "timestamp": 1751647078.64051}}}
{"text": "2025-07-05 00:37:58.699 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:19.204018", "seconds": 19.204018}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:58.699721+08:00", "timestamp": 1751647078.699721}}}
{"text": "2025-07-05 00:37:58.759 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:19.263396", "seconds": 19.263396}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:58.759099+08:00", "timestamp": 1751647078.759099}}}
{"text": "2025-07-05 00:37:59.258 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:19.762950", "seconds": 19.76295}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:59.258653+08:00", "timestamp": 1751647079.258653}}}
{"text": "2025-07-05 00:37:59.318 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:19.822464", "seconds": 19.822464}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:59.318167+08:00", "timestamp": 1751647079.318167}}}
{"text": "2025-07-05 00:37:59.716 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:20.220430", "seconds": 20.22043}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:59.716133+08:00", "timestamp": 1751647079.716133}}}
{"text": "2025-07-05 00:37:59.966 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:20.470448", "seconds": 20.470448}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:37:59.966151+08:00", "timestamp": 1751647079.966151}}}
{"text": "2025-07-05 00:38:00.437 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:20.941761", "seconds": 20.941761}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:00.437464+08:00", "timestamp": 1751647080.437464}}}
{"text": "2025-07-05 00:38:00.980 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:21.485293", "seconds": 21.485293}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:00.980996+08:00", "timestamp": 1751647080.980996}}}
{"text": "2025-07-05 00:38:01.040 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:21.545161", "seconds": 21.545161}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:01.040864+08:00", "timestamp": 1751647081.040864}}}
{"text": "2025-07-05 00:38:02.049 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:22.553957", "seconds": 22.553957}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:02.049660+08:00", "timestamp": 1751647082.04966}}}
{"text": "2025-07-05 00:38:03.040 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:23.545177", "seconds": 23.545177}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:03.040880+08:00", "timestamp": 1751647083.04088}}}
{"text": "2025-07-05 00:38:04.249 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:24.754085", "seconds": 24.754085}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:04.249788+08:00", "timestamp": 1751647084.249788}}}
{"text": "2025-07-05 00:38:04.382 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:24.887112", "seconds": 24.887112}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:04.382815+08:00", "timestamp": 1751647084.382815}}}
{"text": "2025-07-05 00:38:04.442 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:24.946717", "seconds": 24.946717}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:04.442420+08:00", "timestamp": 1751647084.44242}}}
{"text": "2025-07-05 00:38:04.502 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:25.006349", "seconds": 25.006349}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:04.502052+08:00", "timestamp": 1751647084.502052}}}
{"text": "2025-07-05 00:38:04.561 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:25.065888", "seconds": 25.065888}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:04.561591+08:00", "timestamp": 1751647084.561591}}}
{"text": "2025-07-05 00:38:04.870 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:25.375042", "seconds": 25.375042}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:04.870745+08:00", "timestamp": 1751647084.870745}}}
{"text": "2025-07-05 00:38:04.930 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:25.435049", "seconds": 25.435049}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:04.930752+08:00", "timestamp": 1751647084.930752}}}
{"text": "2025-07-05 00:38:04.990 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:25.495291", "seconds": 25.495291}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:04.990994+08:00", "timestamp": 1751647084.990994}}}
{"text": "2025-07-05 00:38:05.110 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:25.614304", "seconds": 25.614304}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:05.110007+08:00", "timestamp": 1751647085.110007}}}
{"text": "2025-07-05 00:38:05.302 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:25.807094", "seconds": 25.807094}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:05.302797+08:00", "timestamp": 1751647085.302797}}}
{"text": "2025-07-05 00:38:05.362 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:25.866319", "seconds": 25.866319}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:05.362022+08:00", "timestamp": 1751647085.362022}}}
{"text": "2025-07-05 00:38:05.421 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:25.926048", "seconds": 25.926048}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:05.421751+08:00", "timestamp": 1751647085.421751}}}
{"text": "2025-07-05 00:38:05.480 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:25.985136", "seconds": 25.985136}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:05.480839+08:00", "timestamp": 1751647085.480839}}}
{"text": "2025-07-05 00:38:05.775 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:26.279674", "seconds": 26.279674}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:05.775377+08:00", "timestamp": 1751647085.775377}}}
{"text": "2025-07-05 00:38:06.071 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:26.575665", "seconds": 26.575665}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:06.071368+08:00", "timestamp": 1751647086.071368}}}
{"text": "2025-07-05 00:38:06.293 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:26.797643", "seconds": 26.797643}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:06.293346+08:00", "timestamp": 1751647086.293346}}}
{"text": "2025-07-05 00:38:06.755 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:27.259340", "seconds": 27.25934}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:06.755043+08:00", "timestamp": 1751647086.755043}}}
{"text": "2025-07-05 00:38:06.815 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:27.319747", "seconds": 27.319747}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:06.815450+08:00", "timestamp": 1751647086.81545}}}
{"text": "2025-07-05 00:38:07.198 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:27.702663", "seconds": 27.702663}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:07.198366+08:00", "timestamp": 1751647087.198366}}}
{"text": "2025-07-05 00:38:07.257 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:27.762217", "seconds": 27.762217}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:07.257920+08:00", "timestamp": 1751647087.25792}}}
{"text": "2025-07-05 00:38:07.479 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:27.983486", "seconds": 27.983486}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:07.479189+08:00", "timestamp": 1751647087.479189}}}
{"text": "2025-07-05 00:38:07.539 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:28.043580", "seconds": 28.04358}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:07.539283+08:00", "timestamp": 1751647087.539283}}}
{"text": "2025-07-05 00:38:07.598 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:28.102525", "seconds": 28.102525}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:07.598228+08:00", "timestamp": 1751647087.598228}}}
{"text": "2025-07-05 00:38:07.657 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:28.162002", "seconds": 28.162002}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:07.657705+08:00", "timestamp": 1751647087.657705}}}
{"text": "2025-07-05 00:38:08.113 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:28.618153", "seconds": 28.618153}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:08.113856+08:00", "timestamp": 1751647088.113856}}}
{"text": "2025-07-05 00:38:08.792 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:29.296496", "seconds": 29.296496}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:08.792199+08:00", "timestamp": 1751647088.792199}}}
{"text": "2025-07-05 00:38:08.852 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:29.356485", "seconds": 29.356485}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:08.852188+08:00", "timestamp": 1751647088.852188}}}
{"text": "2025-07-05 00:38:08.911 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:29.415823", "seconds": 29.415823}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:08.911526+08:00", "timestamp": 1751647088.911526}}}
{"text": "2025-07-05 00:38:08.972 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:29.477084", "seconds": 29.477084}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:08.972787+08:00", "timestamp": 1751647088.972787}}}
{"text": "2025-07-05 00:38:09.031 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:29.535325", "seconds": 29.535325}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:09.031028+08:00", "timestamp": 1751647089.031028}}}
{"text": "2025-07-05 00:38:09.092 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:29.596788", "seconds": 29.596788}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:09.092491+08:00", "timestamp": 1751647089.092491}}}
{"text": "2025-07-05 00:38:09.152 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:29.656999", "seconds": 29.656999}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:09.152702+08:00", "timestamp": 1751647089.152702}}}
{"text": "2025-07-05 00:38:11.086 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:31.590991", "seconds": 31.590991}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:11.086694+08:00", "timestamp": 1751647091.086694}}}
{"text": "2025-07-05 00:38:11.146 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:31.650552", "seconds": 31.650552}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:11.146255+08:00", "timestamp": 1751647091.146255}}}
{"text": "2025-07-05 00:38:11.206 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:31.710861", "seconds": 31.710861}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:11.206564+08:00", "timestamp": 1751647091.206564}}}
{"text": "2025-07-05 00:38:11.265 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:31.770037", "seconds": 31.770037}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:11.265740+08:00", "timestamp": 1751647091.26574}}}
{"text": "2025-07-05 00:38:11.325 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:31.830254", "seconds": 31.830254}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:11.325957+08:00", "timestamp": 1751647091.325957}}}
{"text": "2025-07-05 00:38:11.755 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:32.259578", "seconds": 32.259578}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:11.755281+08:00", "timestamp": 1751647091.755281}}}
{"text": "2025-07-05 00:38:11.815 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:32.319438", "seconds": 32.319438}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:11.815141+08:00", "timestamp": 1751647091.815141}}}
{"text": "2025-07-05 00:38:11.875 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:32.379516", "seconds": 32.379516}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:11.875219+08:00", "timestamp": 1751647091.875219}}}
{"text": "2025-07-05 00:38:12.259 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:32.764241", "seconds": 32.764241}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:12.259944+08:00", "timestamp": 1751647092.259944}}}
{"text": "2025-07-05 00:38:12.319 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:32.824233", "seconds": 32.824233}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:12.319936+08:00", "timestamp": 1751647092.319936}}}
{"text": "2025-07-05 00:38:12.600 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:33.105022", "seconds": 33.105022}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:12.600725+08:00", "timestamp": 1751647092.600725}}}
{"text": "2025-07-05 00:38:12.660 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:33.164884", "seconds": 33.164884}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:12.660587+08:00", "timestamp": 1751647092.660587}}}
{"text": "2025-07-05 00:38:13.119 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:33.623322", "seconds": 33.623322}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:13.119025+08:00", "timestamp": 1751647093.119025}}}
{"text": "2025-07-05 00:38:13.178 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:33.683254", "seconds": 33.683254}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:13.178957+08:00", "timestamp": 1751647093.178957}}}
{"text": "2025-07-05 00:38:13.239 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:33.744102", "seconds": 33.744102}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:13.239805+08:00", "timestamp": 1751647093.239805}}}
{"text": "2025-07-05 00:38:15.181 | DEBUG    | data_juicer.utils.lazy_loader:466 - Loading torch...\n", "record": {"elapsed": {"repr": "0:00:35.685780", "seconds": 35.68578}, "exception": null, "extra": {}, "file": {"name": "lazy_loader.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/lazy_loader.py"}, "function": "_load", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 466, "message": "Loading torch...", "module": "lazy_loader", "name": "data_juicer.utils.lazy_loader", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:15.181483+08:00", "timestamp": 1751647095.181483}}}
{"text": "2025-07-05 00:38:15.402 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'fork'\n", "record": {"elapsed": {"repr": "0:00:35.906513", "seconds": 35.906513}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'fork'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:15.402216+08:00", "timestamp": 1751647095.402216}}}
{"text": "2025-07-05 00:38:15.456 | DEBUG    | data_juicer.ops.base_op:216 - Op [words_num_filter] running with number of procs:1\n", "record": {"elapsed": {"repr": "0:00:35.960722", "seconds": 35.960722}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [words_num_filter] running with number of procs:1", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:15.456425+08:00", "timestamp": 1751647095.456425}}}
{"text": "2025-07-05 00:38:15.470 | DEBUG    | data_juicer.ops.base_op:216 - Op [words_num_filter] running with number of procs:1\n", "record": {"elapsed": {"repr": "0:00:35.974331", "seconds": 35.974331}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [words_num_filter] running with number of procs:1", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:15.470034+08:00", "timestamp": 1751647095.470034}}}
{"text": "2025-07-05 00:38:15.475 | DEBUG    | data_juicer.ops.base_op:216 - Op [words_num_filter] running with number of procs:1\n", "record": {"elapsed": {"repr": "0:00:35.979734", "seconds": 35.979734}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [words_num_filter] running with number of procs:1", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:15.475437+08:00", "timestamp": 1751647095.475437}}}
{"text": "2025-07-05 00:38:16.066 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'fork'\n", "record": {"elapsed": {"repr": "0:00:36.570592", "seconds": 36.570592}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'fork'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:16.066295+08:00", "timestamp": 1751647096.066295}}}
{"text": "2025-07-05 00:38:16.107 | DEBUG    | data_juicer.ops.base_op:216 - Op [word_repetition_filter] running with number of procs:1\n", "record": {"elapsed": {"repr": "0:00:36.611543", "seconds": 36.611543}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [word_repetition_filter] running with number of procs:1", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:16.107246+08:00", "timestamp": 1751647096.107246}}}
{"text": "2025-07-05 00:38:16.116 | DEBUG    | data_juicer.ops.base_op:216 - Op [word_repetition_filter] running with number of procs:1\n", "record": {"elapsed": {"repr": "0:00:36.621210", "seconds": 36.62121}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [word_repetition_filter] running with number of procs:1", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 72642, "name": "MainProcess"}, "thread": {"id": 128371011036224, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:38:16.116913+08:00", "timestamp": 1751647096.116913}}}
