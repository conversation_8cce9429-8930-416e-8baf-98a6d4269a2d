2025-07-05 00:37:41.397 | INFO     | data_juicer.config.config:init_setup_from_cfg:577 - dataset_path config is set and a valid local path
2025-07-05 00:37:41.397 | DEBUG    | data_juicer.config.config:timing_context:35 - Initializing setup from config took 0.01 seconds
2025-07-05 00:37:41.415 | DEBUG    | data_juicer.config.config:timing_context:35 - Updating operator process took 0.02 seconds
2025-07-05 00:37:41.415 | INFO     | data_juicer.config.config:config_backup:879 - Back up the input config file [/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/solution/blip2_prompt_test.yaml] into the work_dir [/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data]
2025-07-05 00:37:41.419 | INFO     | data_juicer.config.config:display_config:901 - Configuration table: 
╒══════════════════════════╤════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╕
│ key                      │ values                                                                                                                     │
╞══════════════════════════╪════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╡
│ config                   │ [Path_fr(/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/solution/blip2_prompt_test.yaml)]                           │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ auto                     │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ auto_num                 │ 1000                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ hpo_config               │ None                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ data_probe_algo          │ 'uniform'                                                                                                                  │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ data_probe_ratio         │ 1.0                                                                                                                        │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ project_name             │ 'blip2-prompt-test'                                                                                                        │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ executor_type            │ 'default'                                                                                                                  │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ dataset_path             │ '/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data/test_sample_100_prompt.jsonl'                 │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ dataset                  │ []                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ generated_dataset_config │ None                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ validators               │ []                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ work_dir                 │ '/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data'                                              │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ export_path              │ '/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data/blip2_prompt_test_result.jsonl'               │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ export_shard_size        │ 0                                                                                                                          │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ export_in_parallel       │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ keep_stats_in_res_ds     │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ keep_hashes_in_res_ds    │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ np                       │ 1                                                                                                                          │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ text_keys                │ 'text'                                                                                                                     │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ image_key                │ 'images'                                                                                                                   │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ image_special_token      │ '<__dj__image>'                                                                                                            │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ audio_key                │ 'audios'                                                                                                                   │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ audio_special_token      │ '<__dj__audio>'                                                                                                            │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ video_key                │ 'videos'                                                                                                                   │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ video_special_token      │ '<__dj__video>'                                                                                                            │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ eoc_special_token        │ '<|__dj__eoc|>'                                                                                                            │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ suffixes                 │ []                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ turbo                    │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ skip_op_error            │ True                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ use_cache                │ True                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ ds_cache_dir             │ '/home/<USER>/.cache/huggingface/datasets'                                                                                  │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ cache_compress           │ None                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ open_monitor             │ True                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ use_checkpoint           │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ temp_dir                 │ None                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ open_tracer              │ True                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ op_list_to_trace         │ []                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ trace_num                │ 10                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ open_insight_mining      │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ op_list_to_mine          │ []                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ op_fusion                │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ fusion_strategy          │ 'probe'                                                                                                                    │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ adaptive_batch_size      │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ process                  │ [{'image_captioning_mapper': {'accelerator': None,                                                                         │
│                          │                               'audio_key': 'audios',                                                                       │
│                          │                               'batch_size': 1,                                                                             │
│                          │                               'caption_num': 1,                                                                            │
│                          │                               'cpu_required': 1,                                                                           │
│                          │                               'hf_img2seq': '/home/<USER>/.cache/modelscope/hub/models/goldsj/blip2-opt-2.7b',              │
│                          │                               'history_key': 'history',                                                                    │
│                          │                               'image_key': 'images',                                                                       │
│                          │                               'index_key': None,                                                                           │
│                          │                               'keep_candidate_mode': 'random_any',                                                         │
│                          │                               'keep_original_sample': False,                                                               │
│                          │                               'mem_required': 8,                                                                           │
│                          │                               'num_proc': 1,                                                                               │
│                          │                               'prompt': 'Provide a comprehensive description '                                             │
│                          │                                         'of this image. Include: 1) Main '                                                 │
│                          │                                         'objects and their specific details '                                              │
│                          │                                         '(colors, materials, shapes, sizes), '                                             │
│                          │                                         '2) Any visible text, brands, or '                                                 │
│                          │                                         'labels, 3) Quantities and '                                                       │
│                          │                                         'positioning, 4) Background and '                                                  │
│                          │                                         'setting, 5) Notable features or '                                                 │
│                          │                                         'characteristics. Be detailed and '                                                │
│                          │                                         'precise to capture all important '                                                │
│                          │                                         'visual information.',                                                             │
│                          │                               'prompt_key': None,                                                                          │
│                          │                               'query_key': 'query',                                                                        │
│                          │                               'response_key': 'response',                                                                  │
│                          │                               'skip_op_error': True,                                                                       │
│                          │                               'text_key': 'text',                                                                          │
│                          │                               'trust_remote_code': False,                                                                  │
│                          │                               'turbo': False,                                                                              │
│                          │                               'video_key': 'videos',                                                                       │
│                          │                               'work_dir': '/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data'}}, │
│                          │  {'words_num_filter': {'accelerator': None,                                                                                │
│                          │                        'audio_key': 'audios',                                                                              │
│                          │                        'batch_size': 1000,                                                                                 │
│                          │                        'cpu_required': 1,                                                                                  │
│                          │                        'history_key': 'history',                                                                           │
│                          │                        'image_key': 'images',                                                                              │
│                          │                        'index_key': None,                                                                                  │
│                          │                        'lang': 'en',                                                                                       │
│                          │                        'max_num': 150,                                                                                     │
│                          │                        'mem_required': 0,                                                                                  │
│                          │                        'min_num': 8,                                                                                       │
│                          │                        'num_proc': 1,                                                                                      │
│                          │                        'query_key': 'query',                                                                               │
│                          │                        'response_key': 'response',                                                                         │
│                          │                        'skip_op_error': True,                                                                              │
│                          │                        'stats_export_path': None,                                                                          │
│                          │                        'text_key': 'text',                                                                                 │
│                          │                        'tokenization': False,                                                                              │
│                          │                        'turbo': False,                                                                                     │
│                          │                        'video_key': 'videos',                                                                              │
│                          │                        'work_dir': '/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data'}},        │
│                          │  {'word_repetition_filter': {'accelerator': None,                                                                          │
│                          │                              'audio_key': 'audios',                                                                        │
│                          │                              'batch_size': 1000,                                                                           │
│                          │                              'cpu_required': 1,                                                                            │
│                          │                              'history_key': 'history',                                                                     │
│                          │                              'image_key': 'images',                                                                        │
│                          │                              'index_key': None,                                                                            │
│                          │                              'lang': 'en',                                                                                 │
│                          │                              'max_ratio': 0.3,                                                                             │
│                          │                              'mem_required': 0,                                                                            │
│                          │                              'min_ratio': 0.0,                                                                             │
│                          │                              'num_proc': 1,                                                                                │
│                          │                              'query_key': 'query',                                                                         │
│                          │                              'rep_len': 1,                                                                                 │
│                          │                              'response_key': 'response',                                                                   │
│                          │                              'skip_op_error': True,                                                                        │
│                          │                              'stats_export_path': None,                                                                    │
│                          │                              'text_key': 'text',                                                                           │
│                          │                              'tokenization': False,                                                                        │
│                          │                              'turbo': False,                                                                               │
│                          │                              'video_key': 'videos',                                                                        │
│                          │                              'work_dir': '/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data'}}]  │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ percentiles              │ []                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ export_original_dataset  │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ save_stats_in_one_file   │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ ray_address              │ 'auto'                                                                                                                     │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ debug                    │ False                                                                                                                      │
╘══════════════════════════╧════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╛
2025-07-05 00:37:41.420 | DEBUG    | data_juicer.config.config:timing_context:35 - Total config initialization time took 0.08 seconds
2025-07-05 00:37:41.420 | INFO     | __main__:timing_context:15 - Loading configuration took 0.08 seconds
2025-07-05 00:37:41.478 | INFO     | data_juicer.core.executor.default_executor:__init__:50 - Using cache compression method: [None]
2025-07-05 00:37:41.479 | INFO     | data_juicer.core.executor.default_executor:__init__:55 - Setting up dataset builder...
2025-07-05 00:37:41.479 | INFO     | data_juicer.core.data.dataset_builder:__init__:37 - found dataset_path setting: /home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data/test_sample_100_prompt.jsonl
2025-07-05 00:37:41.479 | INFO     | data_juicer.core.data.load_strategy:get_strategy_class:79 - Getting strategy class for exec: default, data_type: local, data_source: None
2025-07-05 00:37:41.479 | INFO     | data_juicer.core.executor.default_executor:__init__:74 - Preparing exporter...
2025-07-05 00:37:41.479 | INFO     | data_juicer.core.executor.default_executor:__init__:86 - Preparing tracer...
2025-07-05 00:37:41.480 | INFO     | data_juicer.core.executor.default_executor:__init__:90 - Trace for all ops.
2025-07-05 00:37:41.480 | INFO     | __main__:timing_context:15 - Initializing executor took 0.06 seconds
2025-07-05 00:37:41.480 | INFO     | data_juicer.core.executor.default_executor:run:112 - Loading dataset from dataset builder...

Generating jsonl split: 0 examples [00:00, ? examples/s]
Generating jsonl split: 100 examples [00:00, 60410.54 examples/s]
2025-07-05 00:37:42.276 | INFO     | data_juicer.format.formatter:unify_format:188 - Unifying the input dataset formats...
2025-07-05 00:37:42.276 | INFO     | data_juicer.format.formatter:unify_format:203 - There are 100 sample(s) in the original dataset.

Filter:   0%|          | 0/100 [00:00<?, ? examples/s]
Filter: 100%|##########| 100/100 [00:00<00:00, 44501.90 examples/s]
2025-07-05 00:37:42.279 | INFO     | data_juicer.format.formatter:unify_format:217 - 100 samples left after filtering empty text.
2025-07-05 00:37:42.279 | INFO     | data_juicer.format.formatter:unify_format:248 - Converting relative paths in the dataset to their absolute version. (Based on the directory of input dataset file)

Map:   0%|          | 0/100 [00:00<?, ? examples/s]
Map: 100%|##########| 100/100 [00:00<00:00, 18581.89 examples/s]
2025-07-05 00:37:42.287 | INFO     | data_juicer.core.executor.default_executor:run:118 - Preparing process operators...
2025-07-05 00:37:42.287 | INFO     | data_juicer.core.executor.default_executor:run:146 - Processing data...
2025-07-05 00:37:42.288 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:42.288 | DEBUG    | data_juicer.utils.lazy_loader:_load:466 - Loading torch...
2025-07-05 00:37:42.288 | DEBUG    | data_juicer.utils.process_utils:setup_mp:30 - Setting multiprocess start method to 'forkserver'
2025-07-05 00:37:42.308 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:42.322 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:42.322 | DEBUG    | data_juicer.ops.base_op:runtime_np:216 - Op [image_captioning_mapper] running with number of procs:1
2025-07-05 00:37:42.323 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:42.325 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   0%|          | 0/100 [00:00<?, ? examples/s]2025-07-05 00:37:42.334 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:42.334 | DEBUG    | data_juicer.utils.model_utils:get_model:1110 - functools.partial(<function prepare_huggingface_model at 0x74c04c14a710>, pretrained_model_name_or_path='/home/<USER>/.cache/modelscope/hub/models/goldsj/blip2-opt-2.7b', trust_remote_code=False) not found in MODEL_ZOO (MainProcess)
2025-07-05 00:37:42.335 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:42.335 | DEBUG    | data_juicer.utils.lazy_loader:_load:466 - Loading transformers...


Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s][A

Loading checkpoint shards:  50%|#####     | 1/2 [00:01<00:01,  1.15s/it][A

Loading checkpoint shards: 100%|##########| 2/2 [00:01<00:00,  1.18it/s][A
Loading checkpoint shards: 100%|##########| 2/2 [00:01<00:00,  1.12it/s]

image_captioning_mapper_process:   1%|1         | 1/100 [00:03<05:15,  3.19s/ examples]2025-07-05 00:37:45.518 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:45.578 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   3%|3         | 3/100 [00:03<01:24,  1.15 examples/s]2025-07-05 00:37:45.638 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   4%|4         | 4/100 [00:03<01:15,  1.27 examples/s]2025-07-05 00:37:46.261 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:46.321 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   6%|6         | 6/100 [00:04<00:39,  2.36 examples/s]2025-07-05 00:37:46.380 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:46.440 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   8%|8         | 8/100 [00:04<00:28,  3.23 examples/s]2025-07-05 00:37:46.675 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   9%|9         | 9/100 [00:05<00:40,  2.26 examples/s]2025-07-05 00:37:47.562 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  10%|#         | 10/100 [00:05<00:35,  2.54 examples/s]2025-07-05 00:37:47.812 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  11%|#1        | 11/100 [00:06<00:40,  2.19 examples/s]2025-07-05 00:37:48.446 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:48.506 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  13%|#3        | 13/100 [00:06<00:37,  2.33 examples/s]2025-07-05 00:37:49.227 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:49.287 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  15%|#5        | 15/100 [00:07<00:24,  3.46 examples/s]2025-07-05 00:37:49.346 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  16%|#6        | 16/100 [00:07<00:21,  3.86 examples/s]2025-07-05 00:37:49.495 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:49.556 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  18%|#8        | 18/100 [00:07<00:15,  5.13 examples/s]2025-07-05 00:37:49.675 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  19%|#9        | 19/100 [00:07<00:14,  5.70 examples/s]2025-07-05 00:37:49.778 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:49.838 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  21%|##1       | 21/100 [00:08<00:30,  2.56 examples/s]2025-07-05 00:37:51.270 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:51.330 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  23%|##3       | 23/100 [00:09<00:21,  3.64 examples/s]2025-07-05 00:37:51.389 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:51.448 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  25%|##5       | 25/100 [00:09<00:15,  4.93 examples/s]2025-07-05 00:37:51.508 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:51.567 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  27%|##7       | 27/100 [00:09<00:16,  4.50 examples/s]2025-07-05 00:37:52.034 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  28%|##8       | 28/100 [00:10<00:17,  4.05 examples/s]2025-07-05 00:37:52.387 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  29%|##9       | 29/100 [00:10<00:18,  3.76 examples/s]2025-07-05 00:37:52.725 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  30%|###       | 30/100 [00:10<00:16,  4.25 examples/s]2025-07-05 00:37:52.858 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:52.917 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  32%|###2      | 32/100 [00:11<00:16,  4.03 examples/s]2025-07-05 00:37:53.394 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:53.455 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  34%|###4      | 34/100 [00:12<00:28,  2.29 examples/s]2025-07-05 00:37:54.926 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  35%|###5      | 35/100 [00:13<00:31,  2.10 examples/s]2025-07-05 00:37:55.561 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  36%|###6      | 36/100 [00:13<00:30,  2.12 examples/s]2025-07-05 00:37:56.018 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  37%|###7      | 37/100 [00:15<00:52,  1.20 examples/s]2025-07-05 00:37:57.959 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:58.019 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  39%|###9      | 39/100 [00:15<00:31,  1.94 examples/s]2025-07-05 00:37:58.079 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  40%|####      | 40/100 [00:16<00:30,  1.96 examples/s]2025-07-05 00:37:58.580 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:58.640 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  42%|####2     | 42/100 [00:16<00:19,  3.00 examples/s]2025-07-05 00:37:58.699 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:58.759 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  44%|####4     | 44/100 [00:16<00:17,  3.18 examples/s]2025-07-05 00:37:59.258 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:37:59.318 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  46%|####6     | 46/100 [00:17<00:15,  3.51 examples/s]2025-07-05 00:37:59.716 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  47%|####6     | 47/100 [00:17<00:14,  3.59 examples/s]2025-07-05 00:37:59.966 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  48%|####8     | 48/100 [00:18<00:16,  3.12 examples/s]2025-07-05 00:38:00.437 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  49%|####9     | 49/100 [00:18<00:19,  2.68 examples/s]2025-07-05 00:38:00.980 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:01.040 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  51%|#####1    | 51/100 [00:19<00:21,  2.28 examples/s]2025-07-05 00:38:02.049 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  52%|#####2    | 52/100 [00:20<00:26,  1.78 examples/s]2025-07-05 00:38:03.040 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  53%|#####3    | 53/100 [00:21<00:33,  1.39 examples/s]2025-07-05 00:38:04.249 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  54%|#####4    | 54/100 [00:22<00:26,  1.76 examples/s]2025-07-05 00:38:04.382 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:04.442 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  56%|#####6    | 56/100 [00:22<00:15,  2.83 examples/s]2025-07-05 00:38:04.502 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:04.561 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  58%|#####8    | 58/100 [00:22<00:12,  3.46 examples/s]2025-07-05 00:38:04.870 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:04.930 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  60%|######    | 60/100 [00:22<00:08,  4.78 examples/s]2025-07-05 00:38:04.990 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  61%|######1   | 61/100 [00:22<00:07,  5.23 examples/s]2025-07-05 00:38:05.110 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  62%|######2   | 62/100 [00:22<00:07,  5.22 examples/s]2025-07-05 00:38:05.302 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:05.362 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  64%|######4   | 64/100 [00:23<00:05,  7.13 examples/s]2025-07-05 00:38:05.421 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:05.480 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  66%|######6   | 66/100 [00:23<00:05,  6.52 examples/s]2025-07-05 00:38:05.775 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  67%|######7   | 67/100 [00:23<00:06,  5.49 examples/s]2025-07-05 00:38:06.071 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  68%|######8   | 68/100 [00:23<00:06,  5.23 examples/s]2025-07-05 00:38:06.293 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  69%|######9   | 69/100 [00:24<00:07,  3.89 examples/s]2025-07-05 00:38:06.755 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:06.815 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  71%|#######1  | 71/100 [00:24<00:07,  4.12 examples/s]2025-07-05 00:38:07.198 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:07.257 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  73%|#######3  | 73/100 [00:25<00:05,  4.88 examples/s]2025-07-05 00:38:07.479 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:07.539 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  75%|#######5  | 75/100 [00:25<00:03,  6.47 examples/s]2025-07-05 00:38:07.598 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:07.657 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  77%|#######7  | 77/100 [00:25<00:04,  5.30 examples/s]2025-07-05 00:38:08.113 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  78%|#######8  | 78/100 [00:26<00:06,  3.54 examples/s]2025-07-05 00:38:08.792 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:08.852 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  80%|########  | 80/100 [00:26<00:04,  4.91 examples/s]2025-07-05 00:38:08.911 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:08.972 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  82%|########2 | 82/100 [00:26<00:02,  6.43 examples/s]2025-07-05 00:38:09.031 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:09.092 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  84%|########4 | 84/100 [00:26<00:01,  8.01 examples/s]2025-07-05 00:38:09.152 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:11.086 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  86%|########6 | 86/100 [00:28<00:05,  2.50 examples/s]2025-07-05 00:38:11.146 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:11.206 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  88%|########8 | 88/100 [00:28<00:03,  3.40 examples/s]2025-07-05 00:38:11.265 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:11.325 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  90%|######### | 90/100 [00:29<00:02,  3.58 examples/s]2025-07-05 00:38:11.755 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:11.815 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  92%|#########2| 92/100 [00:29<00:01,  4.71 examples/s]2025-07-05 00:38:11.875 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:12.259 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  94%|#########3| 94/100 [00:29<00:01,  4.65 examples/s]2025-07-05 00:38:12.319 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  95%|#########5| 95/100 [00:30<00:01,  4.41 examples/s]2025-07-05 00:38:12.600 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:12.660 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  97%|#########7| 97/100 [00:30<00:00,  4.20 examples/s]2025-07-05 00:38:13.119 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:38:13.178 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  99%|#########9| 99/100 [00:30<00:00,  5.55 examples/s]2025-07-05 00:38:13.239 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process: 100%|##########| 100/100 [00:32<00:00,  1.95 examples/s]
image_captioning_mapper_process: 100%|##########| 100/100 [00:32<00:00,  3.04 examples/s]
2025-07-05 00:38:15.181 | DEBUG    | data_juicer.utils.lazy_loader:_load:466 - Loading torch...
2025-07-05 00:38:15.401 | INFO     | data_juicer.core.data.dj_dataset:process:326 - [1/3] OP [image_captioning_mapper] Done in 33.113s. Left 100 samples.
2025-07-05 00:38:15.402 | DEBUG    | data_juicer.utils.process_utils:setup_mp:30 - Setting multiprocess start method to 'fork'
2025-07-05 00:38:15.456 | DEBUG    | data_juicer.ops.base_op:runtime_np:216 - Op [words_num_filter] running with number of procs:1

Adding new column for stats:   0%|          | 0/100 [00:00<?, ? examples/s]
Adding new column for stats: 100%|##########| 100/100 [00:00<00:00, 11633.39 examples/s]
2025-07-05 00:38:15.470 | DEBUG    | data_juicer.ops.base_op:runtime_np:216 - Op [words_num_filter] running with number of procs:1

words_num_filter_compute_stats:   0%|          | 0/100 [00:00<?, ? examples/s]
words_num_filter_compute_stats: 100%|##########| 100/100 [00:00<00:00, 28401.30 examples/s]
2025-07-05 00:38:15.475 | DEBUG    | data_juicer.ops.base_op:runtime_np:216 - Op [words_num_filter] running with number of procs:1

words_num_filter_process:   0%|          | 0/100 [00:00<?, ? examples/s]
words_num_filter_process: 100%|##########| 100/100 [00:00<00:00, 70303.45 examples/s]
2025-07-05 00:38:16.065 | INFO     | data_juicer.core.data.dj_dataset:process:326 - [2/3] OP [words_num_filter] Done in 0.663s. Left 41 samples.
2025-07-05 00:38:16.066 | DEBUG    | data_juicer.utils.process_utils:setup_mp:30 - Setting multiprocess start method to 'fork'
2025-07-05 00:38:16.107 | DEBUG    | data_juicer.ops.base_op:runtime_np:216 - Op [word_repetition_filter] running with number of procs:1

word_repetition_filter_compute_stats:   0%|          | 0/41 [00:00<?, ? examples/s]
word_repetition_filter_compute_stats: 100%|##########| 41/41 [00:00<00:00, 7058.80 examples/s]
2025-07-05 00:38:16.116 | DEBUG    | data_juicer.ops.base_op:runtime_np:216 - Op [word_repetition_filter] running with number of procs:1

word_repetition_filter_process:   0%|          | 0/41 [00:00<?, ? examples/s]
word_repetition_filter_process: 100%|##########| 41/41 [00:00<00:00, 28838.92 examples/s]
2025-07-05 00:38:16.727 | INFO     | data_juicer.core.data.dj_dataset:process:326 - [3/3] OP [word_repetition_filter] Done in 0.661s. Left 31 samples.
2025-07-05 00:38:17.729 | INFO     | data_juicer.utils.logger_utils:make_log_summarization:242 - Processing finished with:
Warnings: 0
Errors: 0

Error/Warning details can be found in the log file [/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data/log/export_blip2_prompt_test_result.jsonl_time_20250705003741.txt] and its related log files.
2025-07-05 00:38:17.729 | INFO     | data_juicer.core.executor.default_executor:run:158 - All OPs are done in 35.442s.
2025-07-05 00:38:17.729 | INFO     | data_juicer.core.executor.default_executor:run:161 - Exporting dataset to disk...
2025-07-05 00:38:17.729 | INFO     | data_juicer.core.exporter:_export_impl:111 - Exporting computed stats into a single file...

Creating json from Arrow format:   0%|          | 0/1 [00:00<?, ?ba/s]
Creating json from Arrow format: 100%|##########| 1/1 [00:00<00:00, 1332.37ba/s]
2025-07-05 00:38:17.733 | INFO     | data_juicer.core.exporter:_export_impl:146 - Export dataset into a single file...

Creating json from Arrow format:   0%|          | 0/1 [00:00<?, ?ba/s]
Creating json from Arrow format: 100%|##########| 1/1 [00:00<00:00, 1059.17ba/s]
2025-07-05 00:38:17.734 | INFO     | __main__:timing_context:15 - Running executor took 36.25 seconds
