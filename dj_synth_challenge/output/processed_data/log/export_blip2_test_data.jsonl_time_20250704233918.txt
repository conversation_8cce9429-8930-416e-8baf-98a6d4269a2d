2025-07-04 23:39:18.859 | INFO     | data_juicer.config.config:init_setup_from_cfg:577 - dataset_path config is set and a valid local path
2025-07-04 23:39:18.859 | DEBUG    | data_juicer.config.config:timing_context:35 - Initializing setup from config took 0.01 seconds
2025-07-04 23:39:18.877 | DEBUG    | data_juicer.config.config:timing_context:35 - Updating operator process took 0.02 seconds
2025-07-04 23:39:18.877 | INFO     | data_juicer.config.config:config_backup:879 - Back up the input config file [/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/solution/blip2_test_synthesis.yaml] into the work_dir [/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data]
2025-07-04 23:39:18.881 | INFO     | data_juicer.config.config:display_config:901 - Configuration table: 
╒══════════════════════════╤════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╕
│ key                      │ values                                                                                                                     │
╞══════════════════════════╪════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╡
│ config                   │ [Path_fr(/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/solution/blip2_test_synthesis.yaml)]                        │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ auto                     │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ auto_num                 │ 1000                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ hpo_config               │ None                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ data_probe_algo          │ 'uniform'                                                                                                                  │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ data_probe_ratio         │ 1.0                                                                                                                        │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ project_name             │ 'blip2-test-synthesis'                                                                                                     │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ executor_type            │ 'default'                                                                                                                  │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ dataset_path             │ '/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data/test_sample_100.jsonl'                        │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ dataset                  │ []                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ generated_dataset_config │ None                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ validators               │ []                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ work_dir                 │ '/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data'                                              │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ export_path              │ '/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data/blip2_test_data.jsonl'                        │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ export_shard_size        │ 0                                                                                                                          │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ export_in_parallel       │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ keep_stats_in_res_ds     │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ keep_hashes_in_res_ds    │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ np                       │ 1                                                                                                                          │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ text_keys                │ 'text'                                                                                                                     │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ image_key                │ 'images'                                                                                                                   │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ image_special_token      │ '<__dj__image>'                                                                                                            │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ audio_key                │ 'audios'                                                                                                                   │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ audio_special_token      │ '<__dj__audio>'                                                                                                            │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ video_key                │ 'videos'                                                                                                                   │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ video_special_token      │ '<__dj__video>'                                                                                                            │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ eoc_special_token        │ '<|__dj__eoc|>'                                                                                                            │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ suffixes                 │ []                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ turbo                    │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ skip_op_error            │ True                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ use_cache                │ True                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ ds_cache_dir             │ '/home/<USER>/.cache/huggingface/datasets'                                                                                  │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ cache_compress           │ None                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ open_monitor             │ True                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ use_checkpoint           │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ temp_dir                 │ None                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ open_tracer              │ True                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ op_list_to_trace         │ []                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ trace_num                │ 10                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ open_insight_mining      │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ op_list_to_mine          │ []                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ op_fusion                │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ fusion_strategy          │ 'probe'                                                                                                                    │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ adaptive_batch_size      │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ process                  │ [{'image_captioning_mapper': {'accelerator': None,                                                                         │
│                          │                               'audio_key': 'audios',                                                                       │
│                          │                               'batch_size': 1,                                                                             │
│                          │                               'caption_num': 1,                                                                            │
│                          │                               'cpu_required': 1,                                                                           │
│                          │                               'hf_img2seq': '/home/<USER>/.cache/modelscope/hub/models/goldsj/blip2-opt-2.7b',              │
│                          │                               'history_key': 'history',                                                                    │
│                          │                               'image_key': 'images',                                                                       │
│                          │                               'index_key': None,                                                                           │
│                          │                               'keep_candidate_mode': 'random_any',                                                         │
│                          │                               'keep_original_sample': False,                                                               │
│                          │                               'mem_required': 8,                                                                           │
│                          │                               'num_proc': 1,                                                                               │
│                          │                               'prompt': None,                                                                              │
│                          │                               'prompt_key': None,                                                                          │
│                          │                               'query_key': 'query',                                                                        │
│                          │                               'response_key': 'response',                                                                  │
│                          │                               'skip_op_error': True,                                                                       │
│                          │                               'text_key': 'text',                                                                          │
│                          │                               'trust_remote_code': False,                                                                  │
│                          │                               'turbo': False,                                                                              │
│                          │                               'video_key': 'videos',                                                                       │
│                          │                               'work_dir': '/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data'}}, │
│                          │  {'words_num_filter': {'accelerator': None,                                                                                │
│                          │                        'audio_key': 'audios',                                                                              │
│                          │                        'batch_size': 1000,                                                                                 │
│                          │                        'cpu_required': 1,                                                                                  │
│                          │                        'history_key': 'history',                                                                           │
│                          │                        'image_key': 'images',                                                                              │
│                          │                        'index_key': None,                                                                                  │
│                          │                        'lang': 'en',                                                                                       │
│                          │                        'max_num': 50,                                                                                      │
│                          │                        'mem_required': 0,                                                                                  │
│                          │                        'min_num': 4,                                                                                       │
│                          │                        'num_proc': 1,                                                                                      │
│                          │                        'query_key': 'query',                                                                               │
│                          │                        'response_key': 'response',                                                                         │
│                          │                        'skip_op_error': True,                                                                              │
│                          │                        'stats_export_path': None,                                                                          │
│                          │                        'text_key': 'text',                                                                                 │
│                          │                        'tokenization': False,                                                                              │
│                          │                        'turbo': False,                                                                                     │
│                          │                        'video_key': 'videos',                                                                              │
│                          │                        'work_dir': '/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data'}},        │
│                          │  {'word_repetition_filter': {'accelerator': None,                                                                          │
│                          │                              'audio_key': 'audios',                                                                        │
│                          │                              'batch_size': 1000,                                                                           │
│                          │                              'cpu_required': 1,                                                                            │
│                          │                              'history_key': 'history',                                                                     │
│                          │                              'image_key': 'images',                                                                        │
│                          │                              'index_key': None,                                                                            │
│                          │                              'lang': 'en',                                                                                 │
│                          │                              'max_ratio': 0.3,                                                                             │
│                          │                              'mem_required': 0,                                                                            │
│                          │                              'min_ratio': 0.0,                                                                             │
│                          │                              'num_proc': 1,                                                                                │
│                          │                              'query_key': 'query',                                                                         │
│                          │                              'rep_len': 1,                                                                                 │
│                          │                              'response_key': 'response',                                                                   │
│                          │                              'skip_op_error': True,                                                                        │
│                          │                              'stats_export_path': None,                                                                    │
│                          │                              'text_key': 'text',                                                                           │
│                          │                              'tokenization': False,                                                                        │
│                          │                              'turbo': False,                                                                               │
│                          │                              'video_key': 'videos',                                                                        │
│                          │                              'work_dir': '/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data'}}]  │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ percentiles              │ []                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ export_original_dataset  │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ save_stats_in_one_file   │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ ray_address              │ 'auto'                                                                                                                     │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ debug                    │ False                                                                                                                      │
╘══════════════════════════╧════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╛
2025-07-04 23:39:18.882 | DEBUG    | data_juicer.config.config:timing_context:35 - Total config initialization time took 0.08 seconds
2025-07-04 23:39:18.882 | INFO     | __main__:timing_context:15 - Loading configuration took 0.08 seconds
2025-07-04 23:39:18.934 | INFO     | data_juicer.core.executor.default_executor:__init__:50 - Using cache compression method: [None]
2025-07-04 23:39:18.934 | INFO     | data_juicer.core.executor.default_executor:__init__:55 - Setting up dataset builder...
2025-07-04 23:39:18.934 | INFO     | data_juicer.core.data.dataset_builder:__init__:37 - found dataset_path setting: /home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data/test_sample_100.jsonl
2025-07-04 23:39:18.934 | INFO     | data_juicer.core.data.load_strategy:get_strategy_class:79 - Getting strategy class for exec: default, data_type: local, data_source: None
2025-07-04 23:39:18.935 | INFO     | data_juicer.core.executor.default_executor:__init__:74 - Preparing exporter...
2025-07-04 23:39:18.935 | INFO     | data_juicer.core.executor.default_executor:__init__:86 - Preparing tracer...
2025-07-04 23:39:18.935 | INFO     | data_juicer.core.executor.default_executor:__init__:90 - Trace for all ops.
2025-07-04 23:39:18.935 | INFO     | __main__:timing_context:15 - Initializing executor took 0.05 seconds
2025-07-04 23:39:18.935 | INFO     | data_juicer.core.executor.default_executor:run:112 - Loading dataset from dataset builder...
2025-07-04 23:39:19.789 | INFO     | data_juicer.format.formatter:unify_format:188 - Unifying the input dataset formats...
2025-07-04 23:39:19.790 | INFO     | data_juicer.format.formatter:unify_format:203 - There are 100 sample(s) in the original dataset.

Filter:   0%|          | 0/100 [00:00<?, ? examples/s]
Filter: 100%|##########| 100/100 [00:00<00:00, 34678.00 examples/s]
2025-07-04 23:39:19.795 | INFO     | data_juicer.format.formatter:unify_format:217 - 100 samples left after filtering empty text.
2025-07-04 23:39:19.795 | INFO     | data_juicer.format.formatter:unify_format:248 - Converting relative paths in the dataset to their absolute version. (Based on the directory of input dataset file)

Map:   0%|          | 0/100 [00:00<?, ? examples/s]
Map: 100%|##########| 100/100 [00:00<00:00, 10383.74 examples/s]
2025-07-04 23:39:19.807 | INFO     | data_juicer.core.executor.default_executor:run:118 - Preparing process operators...
2025-07-04 23:39:19.807 | INFO     | data_juicer.core.executor.default_executor:run:146 - Processing data...
2025-07-04 23:39:19.808 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-04 23:39:19.808 | DEBUG    | data_juicer.utils.lazy_loader:_load:466 - Loading torch...
2025-07-04 23:39:19.808 | DEBUG    | data_juicer.utils.process_utils:setup_mp:30 - Setting multiprocess start method to 'forkserver'
2025-07-04 23:39:19.835 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-04 23:39:19.850 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-04 23:39:19.850 | DEBUG    | data_juicer.ops.base_op:runtime_np:216 - Op [image_captioning_mapper] running with number of procs:1
2025-07-04 23:39:19.851 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-04 23:39:19.851 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   0%|          | 0/100 [00:00<?, ? examples/s]2025-07-04 23:39:19.858 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-04 23:39:19.858 | DEBUG    | data_juicer.utils.model_utils:get_model:1110 - functools.partial(<function prepare_huggingface_model at 0x7025fbb52710>, pretrained_model_name_or_path='/home/<USER>/.cache/modelscope/hub/models/goldsj/blip2-opt-2.7b', trust_remote_code=False) not found in MODEL_ZOO (MainProcess)
2025-07-04 23:39:19.859 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-04 23:39:19.859 | DEBUG    | data_juicer.utils.lazy_loader:_load:466 - Loading transformers...


Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s][A

Loading checkpoint shards:  50%|#####     | 1/2 [00:01<00:01,  1.18s/it][A

Loading checkpoint shards: 100%|##########| 2/2 [00:01<00:00,  1.16it/s][A
Loading checkpoint shards: 100%|##########| 2/2 [00:01<00:00,  1.10it/s]

image_captioning_mapper_process:   1%|1         | 1/100 [00:03<05:09,  3.12s/ examples]2025-07-04 23:39:22.979 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   2%|2         | 2/100 [00:03<02:15,  1.38s/ examples]2025-07-04 23:39:23.134 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   3%|3         | 3/100 [00:03<01:19,  1.22 examples/s]2025-07-04 23:39:23.291 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   4%|4         | 4/100 [00:03<01:00,  1.58 examples/s]2025-07-04 23:39:23.638 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   5%|5         | 5/100 [00:03<00:45,  2.07 examples/s]2025-07-04 23:39:23.852 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   6%|6         | 6/100 [00:04<00:37,  2.52 examples/s]2025-07-04 23:39:24.083 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   7%|7         | 7/100 [00:04<00:31,  2.92 examples/s]2025-07-04 23:39:24.312 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   8%|8         | 8/100 [00:04<00:29,  3.12 examples/s]2025-07-04 23:39:24.585 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   9%|9         | 9/100 [00:05<00:29,  3.04 examples/s]2025-07-04 23:39:24.934 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  10%|#         | 10/100 [00:05<00:28,  3.16 examples/s]2025-07-04 23:39:25.223 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  11%|#1        | 11/100 [00:05<00:27,  3.20 examples/s]2025-07-04 23:39:25.525 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  12%|#2        | 12/100 [00:05<00:25,  3.43 examples/s]2025-07-04 23:39:25.768 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  13%|#3        | 13/100 [00:06<00:20,  4.22 examples/s]2025-07-04 23:39:25.880 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  14%|#4        | 14/100 [00:06<00:18,  4.71 examples/s]2025-07-04 23:39:26.036 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  15%|#5        | 15/100 [00:06<00:18,  4.69 examples/s]2025-07-04 23:39:26.251 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  16%|#6        | 16/100 [00:06<00:19,  4.41 examples/s]2025-07-04 23:39:26.510 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  17%|#7        | 17/100 [00:06<00:17,  4.67 examples/s]2025-07-04 23:39:26.694 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  18%|#8        | 18/100 [00:07<00:17,  4.67 examples/s]2025-07-04 23:39:26.908 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  19%|#9        | 19/100 [00:07<00:20,  4.01 examples/s]2025-07-04 23:39:27.239 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  20%|##        | 20/100 [00:07<00:16,  4.81 examples/s]2025-07-04 23:39:27.350 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  21%|##1       | 21/100 [00:07<00:14,  5.33 examples/s]2025-07-04 23:39:27.491 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  22%|##2       | 22/100 [00:07<00:16,  4.76 examples/s]2025-07-04 23:39:27.754 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  23%|##3       | 23/100 [00:08<00:19,  3.98 examples/s]2025-07-04 23:39:28.101 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  24%|##4       | 24/100 [00:08<00:20,  3.68 examples/s]2025-07-04 23:39:28.421 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  25%|##5       | 25/100 [00:08<00:22,  3.39 examples/s]2025-07-04 23:39:28.770 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  26%|##6       | 26/100 [00:09<00:23,  3.17 examples/s]2025-07-04 23:39:29.132 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  27%|##7       | 27/100 [00:09<00:26,  2.77 examples/s]2025-07-04 23:39:29.600 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  28%|##8       | 28/100 [00:09<00:23,  3.06 examples/s]2025-07-04 23:39:29.846 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  29%|##9       | 29/100 [00:10<00:21,  3.27 examples/s]2025-07-04 23:39:30.105 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  30%|###       | 30/100 [00:10<00:18,  3.77 examples/s]2025-07-04 23:39:30.274 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  31%|###1      | 31/100 [00:10<00:18,  3.73 examples/s]2025-07-04 23:39:30.549 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  32%|###2      | 32/100 [00:10<00:15,  4.27 examples/s]2025-07-04 23:39:30.704 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  33%|###3      | 33/100 [00:11<00:14,  4.55 examples/s]2025-07-04 23:39:30.889 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  34%|###4      | 34/100 [00:11<00:13,  4.88 examples/s]2025-07-04 23:39:31.060 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  35%|###5      | 35/100 [00:11<00:15,  4.27 examples/s]2025-07-04 23:39:31.363 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  36%|###6      | 36/100 [00:11<00:13,  4.65 examples/s]2025-07-04 23:39:31.533 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  37%|###7      | 37/100 [00:11<00:14,  4.22 examples/s]2025-07-04 23:39:31.822 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  38%|###8      | 38/100 [00:12<00:12,  4.80 examples/s]2025-07-04 23:39:31.963 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  39%|###9      | 39/100 [00:12<00:13,  4.38 examples/s]2025-07-04 23:39:32.237 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  40%|####      | 40/100 [00:12<00:12,  4.65 examples/s]2025-07-04 23:39:32.422 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  41%|####1     | 41/100 [00:12<00:15,  3.93 examples/s]2025-07-04 23:39:32.769 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  42%|####2     | 42/100 [00:13<00:16,  3.53 examples/s]2025-07-04 23:39:33.119 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  43%|####3     | 43/100 [00:13<00:15,  3.68 examples/s]2025-07-04 23:39:33.364 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  44%|####4     | 44/100 [00:13<00:14,  3.87 examples/s]2025-07-04 23:39:33.592 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  45%|####5     | 45/100 [00:13<00:13,  4.15 examples/s]2025-07-04 23:39:33.791 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  46%|####6     | 46/100 [00:14<00:14,  3.66 examples/s]2025-07-04 23:39:34.139 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  47%|####6     | 47/100 [00:14<00:13,  3.90 examples/s]2025-07-04 23:39:34.357 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  48%|####8     | 48/100 [00:14<00:12,  4.03 examples/s]2025-07-04 23:39:34.586 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  49%|####9     | 49/100 [00:15<00:14,  3.48 examples/s]2025-07-04 23:39:34.965 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  50%|#####     | 50/100 [00:15<00:12,  4.11 examples/s]2025-07-04 23:39:35.106 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  51%|#####1    | 51/100 [00:15<00:10,  4.61 examples/s]2025-07-04 23:39:35.261 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  52%|#####2    | 52/100 [00:15<00:10,  4.71 examples/s]2025-07-04 23:39:35.462 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  53%|#####3    | 53/100 [00:15<00:11,  4.25 examples/s]2025-07-04 23:39:35.751 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  54%|#####4    | 54/100 [00:16<00:10,  4.53 examples/s]2025-07-04 23:39:35.937 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  55%|#####5    | 55/100 [00:16<00:11,  3.86 examples/s]2025-07-04 23:39:36.286 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  56%|#####6    | 56/100 [00:16<00:13,  3.20 examples/s]2025-07-04 23:39:36.755 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  57%|#####6    | 57/100 [00:17<00:12,  3.41 examples/s]2025-07-04 23:39:36.973 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  58%|#####8    | 58/100 [00:17<00:11,  3.53 examples/s]2025-07-04 23:39:37.232 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  59%|#####8    | 59/100 [00:17<00:10,  3.79 examples/s]2025-07-04 23:39:37.450 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  60%|######    | 60/100 [00:17<00:09,  4.41 examples/s]2025-07-04 23:39:37.591 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  61%|######1   | 61/100 [00:17<00:08,  4.49 examples/s]2025-07-04 23:39:37.805 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  62%|######2   | 62/100 [00:18<00:08,  4.27 examples/s]2025-07-04 23:39:38.065 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  63%|######3   | 63/100 [00:18<00:08,  4.47 examples/s]2025-07-04 23:39:38.265 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  64%|######4   | 64/100 [00:18<00:07,  4.53 examples/s]2025-07-04 23:39:38.479 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  65%|######5   | 65/100 [00:18<00:07,  4.67 examples/s]2025-07-04 23:39:38.677 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  66%|######6   | 66/100 [00:19<00:06,  4.88 examples/s]2025-07-04 23:39:38.860 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  67%|######7   | 67/100 [00:19<00:06,  4.93 examples/s]2025-07-04 23:39:39.058 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  68%|######8   | 68/100 [00:19<00:08,  3.79 examples/s]2025-07-04 23:39:39.465 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  69%|######9   | 69/100 [00:19<00:07,  4.08 examples/s]2025-07-04 23:39:39.665 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  70%|#######   | 70/100 [00:19<00:06,  4.58 examples/s]2025-07-04 23:39:39.822 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  71%|#######1  | 71/100 [00:20<00:06,  4.51 examples/s]2025-07-04 23:39:40.052 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  72%|#######2  | 72/100 [00:20<00:05,  5.05 examples/s]2025-07-04 23:39:40.194 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  73%|#######3  | 73/100 [00:20<00:05,  5.03 examples/s]2025-07-04 23:39:40.395 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  74%|#######4  | 74/100 [00:20<00:04,  5.26 examples/s]2025-07-04 23:39:40.565 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  75%|#######5  | 75/100 [00:20<00:04,  5.86 examples/s]2025-07-04 23:39:40.691 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  76%|#######6  | 76/100 [00:21<00:04,  5.71 examples/s]2025-07-04 23:39:40.876 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  77%|#######7  | 77/100 [00:21<00:03,  6.23 examples/s]2025-07-04 23:39:41.002 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  78%|#######8  | 78/100 [00:21<00:03,  5.51 examples/s]2025-07-04 23:39:41.232 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  79%|#######9  | 79/100 [00:21<00:03,  5.61 examples/s]2025-07-04 23:39:41.403 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  80%|########  | 80/100 [00:21<00:03,  5.54 examples/s]2025-07-04 23:39:41.589 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  81%|########1 | 81/100 [00:21<00:03,  5.24 examples/s]2025-07-04 23:39:41.804 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  82%|########2 | 82/100 [00:22<00:03,  4.82 examples/s]2025-07-04 23:39:42.050 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  83%|########2 | 83/100 [00:22<00:04,  4.24 examples/s]2025-07-04 23:39:42.352 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  84%|########4 | 84/100 [00:22<00:04,  3.64 examples/s]2025-07-04 23:39:42.718 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  85%|########5 | 85/100 [00:23<00:03,  3.82 examples/s]2025-07-04 23:39:42.950 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  86%|########6 | 86/100 [00:23<00:03,  4.43 examples/s]2025-07-04 23:39:43.092 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  87%|########7 | 87/100 [00:23<00:03,  4.08 examples/s]2025-07-04 23:39:43.381 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  88%|########8 | 88/100 [00:23<00:03,  3.87 examples/s]2025-07-04 23:39:43.671 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  89%|########9 | 89/100 [00:23<00:02,  4.40 examples/s]2025-07-04 23:39:43.826 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  90%|######### | 90/100 [00:24<00:02,  4.38 examples/s]2025-07-04 23:39:44.056 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  91%|#########1| 91/100 [00:24<00:01,  4.83 examples/s]2025-07-04 23:39:44.213 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  92%|#########2| 92/100 [00:24<00:01,  4.59 examples/s]2025-07-04 23:39:44.457 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  93%|#########3| 93/100 [00:24<00:01,  5.25 examples/s]2025-07-04 23:39:44.583 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  94%|#########3| 94/100 [00:24<00:01,  4.83 examples/s]2025-07-04 23:39:44.829 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  95%|#########5| 95/100 [00:25<00:00,  5.10 examples/s]2025-07-04 23:39:44.999 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  96%|#########6| 96/100 [00:25<00:00,  5.44 examples/s]2025-07-04 23:39:45.155 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  97%|#########7| 97/100 [00:25<00:00,  5.06 examples/s]2025-07-04 23:39:45.384 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  98%|#########8| 98/100 [00:25<00:00,  4.45 examples/s]2025-07-04 23:39:45.672 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  99%|#########9| 99/100 [00:26<00:00,  4.42 examples/s]2025-07-04 23:39:45.903 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process: 100%|##########| 100/100 [00:26<00:00,  4.77 examples/s]
image_captioning_mapper_process: 100%|##########| 100/100 [00:26<00:00,  3.81 examples/s]
2025-07-04 23:39:46.075 | DEBUG    | data_juicer.utils.lazy_loader:_load:466 - Loading torch...
2025-07-04 23:39:46.332 | INFO     | data_juicer.core.data.dj_dataset:process:326 - [1/3] OP [image_captioning_mapper] Done in 26.523s. Left 100 samples.
2025-07-04 23:39:46.332 | DEBUG    | data_juicer.utils.process_utils:setup_mp:30 - Setting multiprocess start method to 'fork'
2025-07-04 23:39:46.406 | DEBUG    | data_juicer.ops.base_op:runtime_np:216 - Op [words_num_filter] running with number of procs:1

Adding new column for stats:   0%|          | 0/100 [00:00<?, ? examples/s]
Adding new column for stats: 100%|##########| 100/100 [00:00<00:00, 17214.46 examples/s]
2025-07-04 23:39:46.415 | DEBUG    | data_juicer.ops.base_op:runtime_np:216 - Op [words_num_filter] running with number of procs:1

words_num_filter_compute_stats:   0%|          | 0/100 [00:00<?, ? examples/s]
words_num_filter_compute_stats: 100%|##########| 100/100 [00:00<00:00, 29514.49 examples/s]
2025-07-04 23:39:46.420 | DEBUG    | data_juicer.ops.base_op:runtime_np:216 - Op [words_num_filter] running with number of procs:1

words_num_filter_process:   0%|          | 0/100 [00:00<?, ? examples/s]
words_num_filter_process: 100%|##########| 100/100 [00:00<00:00, 67934.95 examples/s]
2025-07-04 23:39:46.424 | WARNING  | data_juicer.core.tracer:trace_filter:133 - Datasets before and after op [words_num_filter] are all the same. Thus no comparison results would be generated.
2025-07-04 23:39:47.009 | INFO     | data_juicer.core.data.dj_dataset:process:326 - [2/3] OP [words_num_filter] Done in 0.677s. Left 100 samples.
2025-07-04 23:39:47.010 | DEBUG    | data_juicer.utils.process_utils:setup_mp:30 - Setting multiprocess start method to 'fork'
2025-07-04 23:39:47.042 | DEBUG    | data_juicer.ops.base_op:runtime_np:216 - Op [word_repetition_filter] running with number of procs:1

word_repetition_filter_compute_stats:   0%|          | 0/100 [00:00<?, ? examples/s]
word_repetition_filter_compute_stats: 100%|##########| 100/100 [00:00<00:00, 16059.67 examples/s]
2025-07-04 23:39:47.053 | DEBUG    | data_juicer.ops.base_op:runtime_np:216 - Op [word_repetition_filter] running with number of procs:1

word_repetition_filter_process:   0%|          | 0/100 [00:00<?, ? examples/s]
word_repetition_filter_process: 100%|##########| 100/100 [00:00<00:00, 62582.87 examples/s]
2025-07-04 23:39:47.057 | WARNING  | data_juicer.core.tracer:trace_filter:168 - There are 9 filtered samples before and after op [word_repetition_filter] -- less than expected 10 samples.
2025-07-04 23:39:47.647 | INFO     | data_juicer.core.data.dj_dataset:process:326 - [3/3] OP [word_repetition_filter] Done in 0.638s. Left 91 samples.
2025-07-04 23:39:48.610 | INFO     | data_juicer.utils.logger_utils:make_log_summarization:242 - Processing finished with:
Warnings: 2
Errors: 0

Error/Warning details can be found in the log file [/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data/log/export_blip2_test_data.jsonl_time_20250704233918.txt] and its related log files.
2025-07-04 23:39:48.611 | INFO     | data_juicer.core.executor.default_executor:run:158 - All OPs are done in 28.803s.
2025-07-04 23:39:48.611 | INFO     | data_juicer.core.executor.default_executor:run:161 - Exporting dataset to disk...
2025-07-04 23:39:48.611 | INFO     | data_juicer.core.exporter:_export_impl:111 - Exporting computed stats into a single file...

Creating json from Arrow format:   0%|          | 0/1 [00:00<?, ?ba/s]
Creating json from Arrow format: 100%|##########| 1/1 [00:00<00:00, 904.14ba/s]
2025-07-04 23:39:48.615 | INFO     | data_juicer.core.exporter:_export_impl:146 - Export dataset into a single file...

Creating json from Arrow format:   0%|          | 0/1 [00:00<?, ?ba/s]
Creating json from Arrow format: 100%|##########| 1/1 [00:00<00:00, 1036.40ba/s]
2025-07-04 23:39:48.617 | INFO     | __main__:timing_context:15 - Running executor took 29.68 seconds
