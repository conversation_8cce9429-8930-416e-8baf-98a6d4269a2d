{"text": "2025-07-04 21:50:05.323 | ERROR    | __main__:35 - An error has been caught in function '<module>', process 'MainProcess' (20763), thread 'MainThread' (137989664420928):\nTraceback (most recent call last):\n\n  File \"/home/<USER>/lhp/miniconda3/envs/Syn0625/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n    return _run_code(code, main_globals, None,\n           │         │     └ {'__name__': '__main__', '__doc__': None, '__package__': 'data_juicer.tools', '__loader__': <_frozen_importlib_external.Sourc...\n           │         └ <code object <module> at 0x7d8036d599a0, file \"/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/tool...\n           └ <function _run_code at 0x7d80380cb130>\n  File \"/home/<USER>/lhp/miniconda3/envs/Syn0625/lib/python3.10/runpy.py\", line 86, in _run_code\n    exec(code, run_globals)\n         │     └ {'__name__': '__main__', '__doc__': None, '__package__': 'data_juicer.tools', '__loader__': <_frozen_importlib_external.Sourc...\n         └ <code object <module> at 0x7d8036d599a0, file \"/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/tool...\n\n> File \"/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/tools/process_data.py\", line 35, in <module>\n    main()\n    └ <function main at 0x7d7ea090dcf0>\n\n  File \"/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/tools/process_data.py\", line 25, in main\n    executor = DefaultExecutor(cfg)\n               │               └ Namespace(config=[Path_fr(/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/solution/basic_data_synthesis.yaml)], auto=Fa...\n               └ <class 'data_juicer.core.executor.default_executor.DefaultExecutor'>\n\n  File \"/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/core/executor/default_executor.py\", line 56, in __init__\n    self.dataset_builder = DatasetBuilder(self.cfg,\n    │                      │              │    └ Namespace(config=[Path_fr(/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/solution/basic_data_synthesis.yaml)], auto=Fa...\n    │                      │              └ <data_juicer.core.executor.default_executor.DefaultExecutor object at 0x7d7ea08eb400>\n    │                      └ <class 'data_juicer.core.data.dataset_builder.DatasetBuilder'>\n    └ <data_juicer.core.executor.default_executor.DefaultExecutor object at 0x7d7ea08eb400>\n\n  File \"/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/core/data/dataset_builder.py\", line 38, in __init__\n    ds_configs = rewrite_cli_datapath(cfg.dataset_path)\n                 │                    │   └ './input/pretrain_stage_1/mgm_pretrain_stage_1.jsonl'\n                 │                    └ Namespace(config=[Path_fr(/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/solution/basic_data_synthesis.yaml)], auto=Fa...\n                 └ <function rewrite_cli_datapath at 0x7d7eac1b0430>\n\n  File \"/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/core/data/dataset_builder.py\", line 213, in rewrite_cli_datapath\n    raise ValueError(\n\nValueError: Unable to load the dataset from [./input/pretrain_stage_1/mgm_pretrain_stage_1.jsonl]. Data-Juicer CLI mode only supports local files w or w/o weights, or huggingface path\n", "record": {"elapsed": {"repr": "0:00:02.004756", "seconds": 2.004756}, "exception": {"type": "ValueError", "value": "Unable to load the dataset from [./input/pretrain_stage_1/mgm_pretrain_stage_1.jsonl]. Data-Juicer CLI mode only supports local files w or w/o weights, or huggingface path", "traceback": true}, "extra": {}, "file": {"name": "process_data.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/tools/process_data.py"}, "function": "<module>", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 35, "message": "An error has been caught in function '<module>', process 'MainProcess' (20763), thread 'MainThread' (137989664420928):", "module": "process_data", "name": "__main__", "process": {"id": 20763, "name": "MainProcess"}, "thread": {"id": 137989664420928, "name": "MainThread"}, "time": {"repr": "2025-07-04 21:50:05.323189+08:00", "timestamp": 1751637005.323189}}}