2025-07-05 00:40:05.063 | INFO     | data_juicer.config.config:init_setup_from_cfg:577 - dataset_path config is set and a valid local path
2025-07-05 00:40:05.063 | DEBUG    | data_juicer.config.config:timing_context:35 - Initializing setup from config took 0.01 seconds
2025-07-05 00:40:05.081 | DEBUG    | data_juicer.config.config:timing_context:35 - Updating operator process took 0.02 seconds
2025-07-05 00:40:05.082 | INFO     | data_juicer.config.config:config_backup:879 - Back up the input config file [/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/solution/blip2_simple_prompt_test.yaml] into the work_dir [/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data]
2025-07-05 00:40:05.086 | INFO     | data_juicer.config.config:display_config:901 - Configuration table: 
╒══════════════════════════╤════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╕
│ key                      │ values                                                                                                                     │
╞══════════════════════════╪════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╡
│ config                   │ [Path_fr(/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/solution/blip2_simple_prompt_test.yaml)]                    │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ auto                     │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ auto_num                 │ 1000                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ hpo_config               │ None                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ data_probe_algo          │ 'uniform'                                                                                                                  │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ data_probe_ratio         │ 1.0                                                                                                                        │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ project_name             │ 'blip2-simple-prompt-test'                                                                                                 │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ executor_type            │ 'default'                                                                                                                  │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ dataset_path             │ '/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data/test_sample_100_prompt.jsonl'                 │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ dataset                  │ []                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ generated_dataset_config │ None                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ validators               │ []                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ work_dir                 │ '/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data'                                              │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ export_path              │ '/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data/blip2_simple_prompt_result.jsonl'             │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ export_shard_size        │ 0                                                                                                                          │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ export_in_parallel       │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ keep_stats_in_res_ds     │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ keep_hashes_in_res_ds    │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ np                       │ 1                                                                                                                          │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ text_keys                │ 'text'                                                                                                                     │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ image_key                │ 'images'                                                                                                                   │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ image_special_token      │ '<__dj__image>'                                                                                                            │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ audio_key                │ 'audios'                                                                                                                   │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ audio_special_token      │ '<__dj__audio>'                                                                                                            │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ video_key                │ 'videos'                                                                                                                   │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ video_special_token      │ '<__dj__video>'                                                                                                            │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ eoc_special_token        │ '<|__dj__eoc|>'                                                                                                            │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ suffixes                 │ []                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ turbo                    │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ skip_op_error            │ True                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ use_cache                │ True                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ ds_cache_dir             │ '/home/<USER>/.cache/huggingface/datasets'                                                                                  │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ cache_compress           │ None                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ open_monitor             │ True                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ use_checkpoint           │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ temp_dir                 │ None                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ open_tracer              │ True                                                                                                                       │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ op_list_to_trace         │ []                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ trace_num                │ 10                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ open_insight_mining      │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ op_list_to_mine          │ []                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ op_fusion                │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ fusion_strategy          │ 'probe'                                                                                                                    │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ adaptive_batch_size      │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ process                  │ [{'image_captioning_mapper': {'accelerator': None,                                                                         │
│                          │                               'audio_key': 'audios',                                                                       │
│                          │                               'batch_size': 1,                                                                             │
│                          │                               'caption_num': 1,                                                                            │
│                          │                               'cpu_required': 1,                                                                           │
│                          │                               'hf_img2seq': '/home/<USER>/.cache/modelscope/hub/models/goldsj/blip2-opt-2.7b',              │
│                          │                               'history_key': 'history',                                                                    │
│                          │                               'image_key': 'images',                                                                       │
│                          │                               'index_key': None,                                                                           │
│                          │                               'keep_candidate_mode': 'random_any',                                                         │
│                          │                               'keep_original_sample': False,                                                               │
│                          │                               'mem_required': 8,                                                                           │
│                          │                               'num_proc': 1,                                                                               │
│                          │                               'prompt': 'Describe this image with details '                                                │
│                          │                                         'about colors, objects, text, and '                                                │
│                          │                                         'materials.',                                                                      │
│                          │                               'prompt_key': None,                                                                          │
│                          │                               'query_key': 'query',                                                                        │
│                          │                               'response_key': 'response',                                                                  │
│                          │                               'skip_op_error': True,                                                                       │
│                          │                               'text_key': 'text',                                                                          │
│                          │                               'trust_remote_code': False,                                                                  │
│                          │                               'turbo': False,                                                                              │
│                          │                               'video_key': 'videos',                                                                       │
│                          │                               'work_dir': '/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data'}}, │
│                          │  {'words_num_filter': {'accelerator': None,                                                                                │
│                          │                        'audio_key': 'audios',                                                                              │
│                          │                        'batch_size': 1000,                                                                                 │
│                          │                        'cpu_required': 1,                                                                                  │
│                          │                        'history_key': 'history',                                                                           │
│                          │                        'image_key': 'images',                                                                              │
│                          │                        'index_key': None,                                                                                  │
│                          │                        'lang': 'en',                                                                                       │
│                          │                        'max_num': 80,                                                                                      │
│                          │                        'mem_required': 0,                                                                                  │
│                          │                        'min_num': 4,                                                                                       │
│                          │                        'num_proc': 1,                                                                                      │
│                          │                        'query_key': 'query',                                                                               │
│                          │                        'response_key': 'response',                                                                         │
│                          │                        'skip_op_error': True,                                                                              │
│                          │                        'stats_export_path': None,                                                                          │
│                          │                        'text_key': 'text',                                                                                 │
│                          │                        'tokenization': False,                                                                              │
│                          │                        'turbo': False,                                                                                     │
│                          │                        'video_key': 'videos',                                                                              │
│                          │                        'work_dir': '/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data'}},        │
│                          │  {'word_repetition_filter': {'accelerator': None,                                                                          │
│                          │                              'audio_key': 'audios',                                                                        │
│                          │                              'batch_size': 1000,                                                                           │
│                          │                              'cpu_required': 1,                                                                            │
│                          │                              'history_key': 'history',                                                                     │
│                          │                              'image_key': 'images',                                                                        │
│                          │                              'index_key': None,                                                                            │
│                          │                              'lang': 'en',                                                                                 │
│                          │                              'max_ratio': 0.3,                                                                             │
│                          │                              'mem_required': 0,                                                                            │
│                          │                              'min_ratio': 0.0,                                                                             │
│                          │                              'num_proc': 1,                                                                                │
│                          │                              'query_key': 'query',                                                                         │
│                          │                              'rep_len': 1,                                                                                 │
│                          │                              'response_key': 'response',                                                                   │
│                          │                              'skip_op_error': True,                                                                        │
│                          │                              'stats_export_path': None,                                                                    │
│                          │                              'text_key': 'text',                                                                           │
│                          │                              'tokenization': False,                                                                        │
│                          │                              'turbo': False,                                                                               │
│                          │                              'video_key': 'videos',                                                                        │
│                          │                              'work_dir': '/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data'}}]  │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ percentiles              │ []                                                                                                                         │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ export_original_dataset  │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ save_stats_in_one_file   │ False                                                                                                                      │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ ray_address              │ 'auto'                                                                                                                     │
├──────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ debug                    │ False                                                                                                                      │
╘══════════════════════════╧════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╛
2025-07-05 00:40:05.086 | DEBUG    | data_juicer.config.config:timing_context:35 - Total config initialization time took 0.08 seconds
2025-07-05 00:40:05.086 | INFO     | __main__:timing_context:15 - Loading configuration took 0.08 seconds
2025-07-05 00:40:05.143 | INFO     | data_juicer.core.executor.default_executor:__init__:50 - Using cache compression method: [None]
2025-07-05 00:40:05.143 | INFO     | data_juicer.core.executor.default_executor:__init__:55 - Setting up dataset builder...
2025-07-05 00:40:05.143 | INFO     | data_juicer.core.data.dataset_builder:__init__:37 - found dataset_path setting: /home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data/test_sample_100_prompt.jsonl
2025-07-05 00:40:05.144 | INFO     | data_juicer.core.data.load_strategy:get_strategy_class:79 - Getting strategy class for exec: default, data_type: local, data_source: None
2025-07-05 00:40:05.144 | INFO     | data_juicer.core.executor.default_executor:__init__:74 - Preparing exporter...
2025-07-05 00:40:05.144 | INFO     | data_juicer.core.executor.default_executor:__init__:86 - Preparing tracer...
2025-07-05 00:40:05.144 | INFO     | data_juicer.core.executor.default_executor:__init__:90 - Trace for all ops.
2025-07-05 00:40:05.144 | INFO     | __main__:timing_context:15 - Initializing executor took 0.06 seconds
2025-07-05 00:40:05.144 | INFO     | data_juicer.core.executor.default_executor:run:112 - Loading dataset from dataset builder...
2025-07-05 00:40:06.104 | INFO     | data_juicer.format.formatter:unify_format:188 - Unifying the input dataset formats...
2025-07-05 00:40:06.104 | INFO     | data_juicer.format.formatter:unify_format:203 - There are 100 sample(s) in the original dataset.
2025-07-05 00:40:06.106 | INFO     | data_juicer.format.formatter:unify_format:217 - 100 samples left after filtering empty text.
2025-07-05 00:40:06.106 | INFO     | data_juicer.format.formatter:unify_format:248 - Converting relative paths in the dataset to their absolute version. (Based on the directory of input dataset file)
2025-07-05 00:40:06.108 | INFO     | data_juicer.core.executor.default_executor:run:118 - Preparing process operators...
2025-07-05 00:40:06.108 | INFO     | data_juicer.core.executor.default_executor:run:146 - Processing data...
2025-07-05 00:40:06.108 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:06.108 | DEBUG    | data_juicer.utils.lazy_loader:_load:466 - Loading torch...
2025-07-05 00:40:06.109 | DEBUG    | data_juicer.utils.process_utils:setup_mp:30 - Setting multiprocess start method to 'forkserver'
2025-07-05 00:40:06.129 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:06.151 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:06.152 | DEBUG    | data_juicer.ops.base_op:runtime_np:216 - Op [image_captioning_mapper] running with number of procs:1
2025-07-05 00:40:06.154 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:06.155 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   0%|          | 0/100 [00:00<?, ? examples/s]2025-07-05 00:40:06.166 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:06.166 | DEBUG    | data_juicer.utils.model_utils:get_model:1110 - functools.partial(<function prepare_huggingface_model at 0x7dfc94b3a710>, pretrained_model_name_or_path='/home/<USER>/.cache/modelscope/hub/models/goldsj/blip2-opt-2.7b', trust_remote_code=False) not found in MODEL_ZOO (MainProcess)
2025-07-05 00:40:06.167 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:06.167 | DEBUG    | data_juicer.utils.lazy_loader:_load:466 - Loading transformers...


Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s][A

Loading checkpoint shards:  50%|#####     | 1/2 [00:01<00:01,  1.16s/it][A

Loading checkpoint shards: 100%|##########| 2/2 [00:01<00:00,  1.17it/s][A
Loading checkpoint shards: 100%|##########| 2/2 [00:01<00:00,  1.11it/s]

image_captioning_mapper_process:   1%|1         | 1/100 [00:02<04:45,  2.88s/ examples]2025-07-05 00:40:09.043 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   2%|2         | 2/100 [00:03<02:03,  1.26s/ examples]2025-07-05 00:40:09.173 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:09.226 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   4%|4         | 4/100 [00:03<00:48,  1.98 examples/s]2025-07-05 00:40:09.278 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:09.328 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   6%|6         | 6/100 [00:03<00:27,  3.45 examples/s]2025-07-05 00:40:09.378 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:09.429 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:   8%|8         | 8/100 [00:03<00:17,  5.17 examples/s]2025-07-05 00:40:09.480 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:09.721 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  10%|#         | 10/100 [00:03<00:19,  4.57 examples/s]2025-07-05 00:40:10.006 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:10.059 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  12%|#2        | 12/100 [00:03<00:14,  6.18 examples/s]2025-07-05 00:40:10.110 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:10.162 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  14%|#4        | 14/100 [00:04<00:10,  7.97 examples/s]2025-07-05 00:40:10.213 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:10.469 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  16%|#6        | 16/100 [00:04<00:11,  7.43 examples/s]2025-07-05 00:40:10.521 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:10.573 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  18%|#8        | 18/100 [00:04<00:10,  8.02 examples/s]2025-07-05 00:40:10.727 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:10.778 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  20%|##        | 20/100 [00:04<00:08,  9.81 examples/s]2025-07-05 00:40:10.829 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:10.881 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  22%|##2       | 22/100 [00:04<00:06, 11.51 examples/s]2025-07-05 00:40:10.935 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:10.988 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  24%|##4       | 24/100 [00:04<00:05, 13.08 examples/s]2025-07-05 00:40:11.040 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:11.092 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  26%|##6       | 26/100 [00:04<00:05, 14.48 examples/s]2025-07-05 00:40:11.145 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:11.197 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  28%|##8       | 28/100 [00:05<00:04, 15.67 examples/s]2025-07-05 00:40:11.248 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:11.416 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  30%|###       | 30/100 [00:05<00:05, 12.86 examples/s]2025-07-05 00:40:11.468 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:11.665 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  32%|###2      | 32/100 [00:05<00:06, 10.90 examples/s]2025-07-05 00:40:11.717 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:11.767 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  34%|###4      | 34/100 [00:05<00:05, 11.33 examples/s]2025-07-05 00:40:11.877 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:12.162 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  36%|###6      | 36/100 [00:06<00:07,  8.90 examples/s]2025-07-05 00:40:12.214 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:12.458 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  38%|###8      | 38/100 [00:06<00:07,  8.12 examples/s]2025-07-05 00:40:12.510 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:12.578 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  40%|####      | 40/100 [00:06<00:06,  9.60 examples/s]2025-07-05 00:40:12.630 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:12.682 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  42%|####2     | 42/100 [00:06<00:05, 11.30 examples/s]2025-07-05 00:40:12.734 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:12.787 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  44%|####4     | 44/100 [00:06<00:04, 12.88 examples/s]2025-07-05 00:40:12.838 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:12.890 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  46%|####6     | 46/100 [00:06<00:03, 14.32 examples/s]2025-07-05 00:40:12.942 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:12.995 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  48%|####8     | 48/100 [00:06<00:03, 15.49 examples/s]2025-07-05 00:40:13.046 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:13.098 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  50%|#####     | 50/100 [00:06<00:03, 16.46 examples/s]2025-07-05 00:40:13.150 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:13.202 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  52%|#####2    | 52/100 [00:07<00:04, 11.20 examples/s]2025-07-05 00:40:13.462 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:13.515 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  54%|#####4    | 54/100 [00:07<00:03, 12.80 examples/s]2025-07-05 00:40:13.566 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:13.751 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  56%|#####6    | 56/100 [00:07<00:03, 11.10 examples/s]2025-07-05 00:40:13.803 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:14.886 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  58%|#####8    | 58/100 [00:08<00:09,  4.28 examples/s]2025-07-05 00:40:14.938 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:14.991 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  60%|######    | 60/100 [00:08<00:07,  5.58 examples/s]2025-07-05 00:40:15.043 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:15.095 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  62%|######2   | 62/100 [00:08<00:05,  7.10 examples/s]2025-07-05 00:40:15.147 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:15.198 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  64%|######4   | 64/100 [00:09<00:04,  8.76 examples/s]2025-07-05 00:40:15.249 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:15.302 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  66%|######6   | 66/100 [00:09<00:03,  8.68 examples/s]2025-07-05 00:40:15.485 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:15.536 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  68%|######8   | 68/100 [00:09<00:03, 10.40 examples/s]2025-07-05 00:40:15.589 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:15.640 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  70%|#######   | 70/100 [00:09<00:02, 12.07 examples/s]2025-07-05 00:40:15.692 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:15.743 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  72%|#######2  | 72/100 [00:09<00:02, 13.62 examples/s]2025-07-05 00:40:15.795 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:15.846 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  74%|#######4  | 74/100 [00:09<00:02,  9.82 examples/s]2025-07-05 00:40:16.132 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:16.184 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  76%|#######6  | 76/100 [00:10<00:02, 11.51 examples/s]2025-07-05 00:40:16.235 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:16.431 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  78%|#######8  | 78/100 [00:10<00:02,  9.59 examples/s]2025-07-05 00:40:16.525 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:16.577 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  80%|########  | 80/100 [00:10<00:01, 10.04 examples/s]2025-07-05 00:40:16.703 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:16.754 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  82%|########2 | 82/100 [00:10<00:01, 11.73 examples/s]2025-07-05 00:40:16.806 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:16.858 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  84%|########4 | 84/100 [00:10<00:01, 13.29 examples/s]2025-07-05 00:40:16.910 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:17.413 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  86%|########6 | 86/100 [00:11<00:02,  6.09 examples/s]2025-07-05 00:40:17.653 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:17.910 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  88%|########8 | 88/100 [00:11<00:01,  6.21 examples/s]2025-07-05 00:40:17.961 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:18.012 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  90%|######### | 90/100 [00:11<00:01,  7.80 examples/s]2025-07-05 00:40:18.064 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:18.116 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  92%|#########2| 92/100 [00:12<00:00,  9.11 examples/s]2025-07-05 00:40:18.197 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:18.248 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  94%|#########3| 94/100 [00:12<00:00, 10.86 examples/s]2025-07-05 00:40:18.299 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:18.614 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  96%|#########6| 96/100 [00:12<00:00,  8.37 examples/s]2025-07-05 00:40:18.665 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:18.717 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process:  98%|#########8| 98/100 [00:12<00:00, 10.09 examples/s]2025-07-05 00:40:18.768 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1
2025-07-05 00:40:18.819 | DEBUG    | data_juicer.utils.availability_utils:_is_package_available:26 - Detected torch version 2.5.1

image_captioning_mapper_process: 100%|##########| 100/100 [00:12<00:00, 11.80 examples/s]
image_captioning_mapper_process: 100%|##########| 100/100 [00:12<00:00,  7.87 examples/s]
2025-07-05 00:40:18.873 | DEBUG    | data_juicer.utils.lazy_loader:_load:466 - Loading torch...
2025-07-05 00:40:19.405 | INFO     | data_juicer.core.data.dj_dataset:process:326 - [1/3] OP [image_captioning_mapper] Done in 13.296s. Left 100 samples.
2025-07-05 00:40:19.405 | DEBUG    | data_juicer.utils.process_utils:setup_mp:30 - Setting multiprocess start method to 'fork'
2025-07-05 00:40:19.463 | DEBUG    | data_juicer.ops.base_op:runtime_np:216 - Op [words_num_filter] running with number of procs:1

Adding new column for stats:   0%|          | 0/100 [00:00<?, ? examples/s]
Adding new column for stats: 100%|##########| 100/100 [00:00<00:00, 16961.07 examples/s]
2025-07-05 00:40:19.472 | DEBUG    | data_juicer.ops.base_op:runtime_np:216 - Op [words_num_filter] running with number of procs:1

words_num_filter_compute_stats:   0%|          | 0/100 [00:00<?, ? examples/s]
words_num_filter_compute_stats: 100%|##########| 100/100 [00:00<00:00, 33426.08 examples/s]
2025-07-05 00:40:19.477 | DEBUG    | data_juicer.ops.base_op:runtime_np:216 - Op [words_num_filter] running with number of procs:1

words_num_filter_process:   0%|          | 0/100 [00:00<?, ? examples/s]
words_num_filter_process: 100%|##########| 100/100 [00:00<00:00, 55304.64 examples/s]
2025-07-05 00:40:20.054 | INFO     | data_juicer.core.data.dj_dataset:process:326 - [2/3] OP [words_num_filter] Done in 0.649s. Left 23 samples.
2025-07-05 00:40:20.054 | DEBUG    | data_juicer.utils.process_utils:setup_mp:30 - Setting multiprocess start method to 'fork'
2025-07-05 00:40:20.115 | DEBUG    | data_juicer.ops.base_op:runtime_np:216 - Op [word_repetition_filter] running with number of procs:1

word_repetition_filter_compute_stats:   0%|          | 0/23 [00:00<?, ? examples/s]
word_repetition_filter_compute_stats: 100%|##########| 23/23 [00:00<00:00, 4420.92 examples/s]
2025-07-05 00:40:20.125 | DEBUG    | data_juicer.ops.base_op:runtime_np:216 - Op [word_repetition_filter] running with number of procs:1

word_repetition_filter_process:   0%|          | 0/23 [00:00<?, ? examples/s]
word_repetition_filter_process: 100%|##########| 23/23 [00:00<00:00, 17229.68 examples/s]
2025-07-05 00:40:20.129 | WARNING  | data_juicer.core.tracer:trace_filter:168 - There are 3 filtered samples before and after op [word_repetition_filter] -- less than expected 10 samples.
2025-07-05 00:40:20.717 | INFO     | data_juicer.core.data.dj_dataset:process:326 - [3/3] OP [word_repetition_filter] Done in 0.663s. Left 20 samples.
2025-07-05 00:40:21.693 | INFO     | data_juicer.utils.logger_utils:make_log_summarization:242 - Processing finished with:
Warnings: 1
Errors: 0

Error/Warning details can be found in the log file [/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/output/processed_data/log/export_blip2_simple_prompt_result.jsonl_time_20250705004005.txt] and its related log files.
2025-07-05 00:40:21.693 | INFO     | data_juicer.core.executor.default_executor:run:158 - All OPs are done in 15.585s.
2025-07-05 00:40:21.693 | INFO     | data_juicer.core.executor.default_executor:run:161 - Exporting dataset to disk...
2025-07-05 00:40:21.693 | INFO     | data_juicer.core.exporter:_export_impl:111 - Exporting computed stats into a single file...

Creating json from Arrow format:   0%|          | 0/1 [00:00<?, ?ba/s]
Creating json from Arrow format: 100%|##########| 1/1 [00:00<00:00, 1119.38ba/s]
2025-07-05 00:40:21.697 | INFO     | data_juicer.core.exporter:_export_impl:146 - Export dataset into a single file...

Creating json from Arrow format:   0%|          | 0/1 [00:00<?, ?ba/s]
Creating json from Arrow format: 100%|##########| 1/1 [00:00<00:00, 1964.55ba/s]
2025-07-05 00:40:21.698 | INFO     | __main__:timing_context:15 - Running executor took 16.55 seconds
