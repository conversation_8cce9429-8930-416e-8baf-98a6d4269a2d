{"text": "2025-07-04 21:50:52.202 | DEBUG    | data_juicer.config.config:35 - Initializing setup from config took 0.01 seconds\n", "record": {"elapsed": {"repr": "0:00:01.914961", "seconds": 1.914961}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Initializing setup from config took 0.01 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 20982, "name": "MainProcess"}, "thread": {"id": 130661081105472, "name": "MainThread"}, "time": {"repr": "2025-07-04 21:50:52.202874+08:00", "timestamp": 1751637052.202874}}}
{"text": "2025-07-04 21:50:52.225 | DEBUG    | data_juicer.config.config:35 - Updating operator process took 0.02 seconds\n", "record": {"elapsed": {"repr": "0:00:01.937108", "seconds": 1.937108}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Updating operator process took 0.02 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 20982, "name": "MainProcess"}, "thread": {"id": 130661081105472, "name": "MainThread"}, "time": {"repr": "2025-07-04 21:50:52.225021+08:00", "timestamp": 1751637052.225021}}}
{"text": "2025-07-04 21:50:52.229 | DEBUG    | data_juicer.config.config:35 - Total config initialization time took 0.09 seconds\n", "record": {"elapsed": {"repr": "0:00:01.941848", "seconds": 1.941848}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Total config initialization time took 0.09 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 20982, "name": "MainProcess"}, "thread": {"id": 130661081105472, "name": "MainThread"}, "time": {"repr": "2025-07-04 21:50:52.229761+08:00", "timestamp": 1751637052.229761}}}
{"text": "2025-07-04 21:50:57.068 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'fork'\n", "record": {"elapsed": {"repr": "0:00:06.780316", "seconds": 6.780316}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'fork'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 20982, "name": "MainProcess"}, "thread": {"id": 130661081105472, "name": "MainThread"}, "time": {"repr": "2025-07-04 21:50:57.068229+08:00", "timestamp": 1751637057.068229}}}
{"text": "2025-07-04 21:50:57.090 | DEBUG    | data_juicer.ops.base_op:216 - Op [text_length_filter] running with number of procs:8\n", "record": {"elapsed": {"repr": "0:00:06.802553", "seconds": 6.802553}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [text_length_filter] running with number of procs:8", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 20982, "name": "MainProcess"}, "thread": {"id": 130661081105472, "name": "MainThread"}, "time": {"repr": "2025-07-04 21:50:57.090466+08:00", "timestamp": 1751637057.090466}}}
{"text": "2025-07-04 21:50:58.415 | DEBUG    | data_juicer.ops.base_op:216 - Op [text_length_filter] running with number of procs:8\n", "record": {"elapsed": {"repr": "0:00:08.127503", "seconds": 8.127503}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [text_length_filter] running with number of procs:8", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 20982, "name": "MainProcess"}, "thread": {"id": 130661081105472, "name": "MainThread"}, "time": {"repr": "2025-07-04 21:50:58.415416+08:00", "timestamp": 1751637058.415416}}}
{"text": "2025-07-04 21:50:58.781 | DEBUG    | data_juicer.ops.base_op:216 - Op [text_length_filter] running with number of procs:8\n", "record": {"elapsed": {"repr": "0:00:08.493335", "seconds": 8.493335}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [text_length_filter] running with number of procs:8", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 20982, "name": "MainProcess"}, "thread": {"id": 130661081105472, "name": "MainThread"}, "time": {"repr": "2025-07-04 21:50:58.781248+08:00", "timestamp": 1751637058.781248}}}
{"text": "2025-07-04 21:50:59.109 | DEBUG    | data_juicer.utils.lazy_loader:466 - Loading torch...\n", "record": {"elapsed": {"repr": "0:00:08.821171", "seconds": 8.821171}, "exception": null, "extra": {}, "file": {"name": "lazy_loader.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/lazy_loader.py"}, "function": "_load", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 466, "message": "Loading torch...", "module": "lazy_loader", "name": "data_juicer.utils.lazy_loader", "process": {"id": 20982, "name": "MainProcess"}, "thread": {"id": 130661081105472, "name": "MainThread"}, "time": {"repr": "2025-07-04 21:50:59.109084+08:00", "timestamp": 1751637059.109084}}}
{"text": "2025-07-04 21:50:59.331 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'fork'\n", "record": {"elapsed": {"repr": "0:00:09.043906", "seconds": 9.043906}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'fork'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 20982, "name": "MainProcess"}, "thread": {"id": 130661081105472, "name": "MainThread"}, "time": {"repr": "2025-07-04 21:50:59.331819+08:00", "timestamp": 1751637059.331819}}}
{"text": "2025-07-04 21:50:59.358 | DEBUG    | data_juicer.ops.base_op:216 - Op [image_shape_filter] running with number of procs:8\n", "record": {"elapsed": {"repr": "0:00:09.070294", "seconds": 9.070294}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [image_shape_filter] running with number of procs:8", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 20982, "name": "MainProcess"}, "thread": {"id": 130661081105472, "name": "MainThread"}, "time": {"repr": "2025-07-04 21:50:59.358207+08:00", "timestamp": 1751637059.358207}}}
{"text": "2025-07-04 21:51:42.852 | DEBUG    | data_juicer.ops.base_op:216 - Op [image_shape_filter] running with number of procs:8\n", "record": {"elapsed": {"repr": "0:00:52.564978", "seconds": 52.564978}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [image_shape_filter] running with number of procs:8", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 20982, "name": "MainProcess"}, "thread": {"id": 130661081105472, "name": "MainThread"}, "time": {"repr": "2025-07-04 21:51:42.852891+08:00", "timestamp": 1751637102.852891}}}
{"text": "2025-07-04 21:51:44.021 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'fork'\n", "record": {"elapsed": {"repr": "0:00:53.733981", "seconds": 53.733981}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'fork'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 20982, "name": "MainProcess"}, "thread": {"id": 130661081105472, "name": "MainThread"}, "time": {"repr": "2025-07-04 21:51:44.021894+08:00", "timestamp": 1751637104.021894}}}
{"text": "2025-07-04 21:51:44.071 | DEBUG    | data_juicer.ops.base_op:216 - Op [character_repetition_filter] running with number of procs:8\n", "record": {"elapsed": {"repr": "0:00:53.783763", "seconds": 53.783763}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [character_repetition_filter] running with number of procs:8", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 20982, "name": "MainProcess"}, "thread": {"id": 130661081105472, "name": "MainThread"}, "time": {"repr": "2025-07-04 21:51:44.071676+08:00", "timestamp": 1751637104.071676}}}
{"text": "2025-07-04 21:51:46.152 | DEBUG    | data_juicer.ops.base_op:216 - Op [character_repetition_filter] running with number of procs:8\n", "record": {"elapsed": {"repr": "0:00:55.864234", "seconds": 55.864234}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [character_repetition_filter] running with number of procs:8", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 20982, "name": "MainProcess"}, "thread": {"id": 130661081105472, "name": "MainThread"}, "time": {"repr": "2025-07-04 21:51:46.152147+08:00", "timestamp": 1751637106.152147}}}
{"text": "2025-07-04 21:51:46.907 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'fork'\n", "record": {"elapsed": {"repr": "0:00:56.619149", "seconds": 56.619149}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'fork'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 20982, "name": "MainProcess"}, "thread": {"id": 130661081105472, "name": "MainThread"}, "time": {"repr": "2025-07-04 21:51:46.907062+08:00", "timestamp": 1751637106.907062}}}
{"text": "2025-07-04 21:51:46.947 | DEBUG    | data_juicer.ops.base_op:216 - Op [word_repetition_filter] running with number of procs:8\n", "record": {"elapsed": {"repr": "0:00:56.659739", "seconds": 56.659739}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [word_repetition_filter] running with number of procs:8", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 20982, "name": "MainProcess"}, "thread": {"id": 130661081105472, "name": "MainThread"}, "time": {"repr": "2025-07-04 21:51:46.947652+08:00", "timestamp": 1751637106.947652}}}
{"text": "2025-07-04 21:51:49.061 | DEBUG    | data_juicer.ops.base_op:216 - Op [word_repetition_filter] running with number of procs:8\n", "record": {"elapsed": {"repr": "0:00:58.773258", "seconds": 58.773258}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [word_repetition_filter] running with number of procs:8", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 20982, "name": "MainProcess"}, "thread": {"id": 130661081105472, "name": "MainThread"}, "time": {"repr": "2025-07-04 21:51:49.061171+08:00", "timestamp": 1751637109.061171}}}
