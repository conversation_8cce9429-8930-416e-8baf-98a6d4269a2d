{"text": "2025-07-05 00:20:02.556 | WARNING  | data_juicer.utils.process_utils:64 - The required cuda memory of Op[image_watermark_filter] has not been specified. Please specify the mem_required field in the config file, or you might encounter CUDA out of memory error. You can reference the mem_required field in the config_all.yaml file.\n", "record": {"elapsed": {"repr": "0:38:06.200925", "seconds": 2286.200925}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "calculate_np", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 64, "message": "The required cuda memory of Op[image_watermark_filter] has not been specified. Please specify the mem_required field in the config file, or you might encounter CUDA out of memory error. You can reference the mem_required field in the config_all.yaml file.", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 42902, "name": "MainProcess"}, "thread": {"id": 136486054831168, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:20:02.556565+08:00", "timestamp": 1751646002.556565}}}
{"text": "2025-07-05 00:21:45.135 | WARNING  | data_juicer.utils.process_utils:64 - The required cuda memory of Op[image_watermark_filter] has not been specified. Please specify the mem_required field in the config file, or you might encounter CUDA out of memory error. You can reference the mem_required field in the config_all.yaml file.\n", "record": {"elapsed": {"repr": "0:39:48.780099", "seconds": 2388.780099}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "calculate_np", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 64, "message": "The required cuda memory of Op[image_watermark_filter] has not been specified. Please specify the mem_required field in the config file, or you might encounter CUDA out of memory error. You can reference the mem_required field in the config_all.yaml file.", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 42902, "name": "MainProcess"}, "thread": {"id": 136486054831168, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:21:45.135739+08:00", "timestamp": 1751646105.135739}}}
