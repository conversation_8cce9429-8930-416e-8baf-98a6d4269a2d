{"text": "2025-07-04 23:39:18.859 | DEBUG    | data_juicer.config.config:35 - Initializing setup from config took 0.01 seconds\n", "record": {"elapsed": {"repr": "0:00:01.888771", "seconds": 1.888771}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Initializing setup from config took 0.01 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:18.859560+08:00", "timestamp": 1751643558.85956}}}
{"text": "2025-07-04 23:39:18.877 | DEBUG    | data_juicer.config.config:35 - Updating operator process took 0.02 seconds\n", "record": {"elapsed": {"repr": "0:00:01.907044", "seconds": 1.907044}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Updating operator process took 0.02 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:18.877833+08:00", "timestamp": 1751643558.877833}}}
{"text": "2025-07-04 23:39:18.882 | DEBUG    | data_juicer.config.config:35 - Total config initialization time took 0.08 seconds\n", "record": {"elapsed": {"repr": "0:00:01.911768", "seconds": 1.911768}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Total config initialization time took 0.08 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:18.882557+08:00", "timestamp": 1751643558.882557}}}
{"text": "2025-07-04 23:39:19.808 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:02.837645", "seconds": 2.837645}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:19.808434+08:00", "timestamp": 1751643559.808434}}}
{"text": "2025-07-04 23:39:19.808 | DEBUG    | data_juicer.utils.lazy_loader:466 - Loading torch...\n", "record": {"elapsed": {"repr": "0:00:02.837752", "seconds": 2.837752}, "exception": null, "extra": {}, "file": {"name": "lazy_loader.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/lazy_loader.py"}, "function": "_load", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 466, "message": "Loading torch...", "module": "lazy_loader", "name": "data_juicer.utils.lazy_loader", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:19.808541+08:00", "timestamp": 1751643559.808541}}}
{"text": "2025-07-04 23:39:19.808 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'forkserver'\n", "record": {"elapsed": {"repr": "0:00:02.838078", "seconds": 2.838078}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'forkserver'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:19.808867+08:00", "timestamp": 1751643559.808867}}}
{"text": "2025-07-04 23:39:19.835 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:02.864740", "seconds": 2.86474}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:19.835529+08:00", "timestamp": 1751643559.835529}}}
{"text": "2025-07-04 23:39:19.850 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:02.879719", "seconds": 2.879719}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:19.850508+08:00", "timestamp": 1751643559.850508}}}
{"text": "2025-07-04 23:39:19.850 | DEBUG    | data_juicer.ops.base_op:216 - Op [image_captioning_mapper] running with number of procs:1\n", "record": {"elapsed": {"repr": "0:00:02.879906", "seconds": 2.879906}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [image_captioning_mapper] running with number of procs:1", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:19.850695+08:00", "timestamp": 1751643559.850695}}}
{"text": "2025-07-04 23:39:19.851 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:02.880372", "seconds": 2.880372}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:19.851161+08:00", "timestamp": 1751643559.851161}}}
{"text": "2025-07-04 23:39:19.851 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:02.880829", "seconds": 2.880829}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:19.851618+08:00", "timestamp": 1751643559.851618}}}
{"text": "2025-07-04 23:39:19.858 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:02.887736", "seconds": 2.887736}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:19.858525+08:00", "timestamp": 1751643559.858525}}}
{"text": "2025-07-04 23:39:19.858 | DEBUG    | data_juicer.utils.model_utils:1110 - functools.partial(<function prepare_huggingface_model at 0x7025fbb52710>, pretrained_model_name_or_path='/home/<USER>/.cache/modelscope/hub/models/goldsj/blip2-opt-2.7b', trust_remote_code=False) not found in MODEL_ZOO (MainProcess)\n", "record": {"elapsed": {"repr": "0:00:02.887880", "seconds": 2.88788}, "exception": null, "extra": {}, "file": {"name": "model_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/model_utils.py"}, "function": "get_model", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 1110, "message": "functools.partial(<function prepare_huggingface_model at 0x7025fbb52710>, pretrained_model_name_or_path='/home/<USER>/.cache/modelscope/hub/models/goldsj/blip2-opt-2.7b', trust_remote_code=False) not found in MODEL_ZOO (MainProcess)", "module": "model_utils", "name": "data_juicer.utils.model_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:19.858669+08:00", "timestamp": 1751643559.858669}}}
{"text": "2025-07-04 23:39:19.859 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:02.888660", "seconds": 2.88866}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:19.859449+08:00", "timestamp": 1751643559.859449}}}
{"text": "2025-07-04 23:39:19.859 | DEBUG    | data_juicer.utils.lazy_loader:466 - Loading transformers...\n", "record": {"elapsed": {"repr": "0:00:02.888873", "seconds": 2.888873}, "exception": null, "extra": {}, "file": {"name": "lazy_loader.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/lazy_loader.py"}, "function": "_load", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 466, "message": "Loading transformers...", "module": "lazy_loader", "name": "data_juicer.utils.lazy_loader", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:19.859662+08:00", "timestamp": 1751643559.859662}}}
{"text": "2025-07-04 23:39:22.979 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.008494", "seconds": 6.008494}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:22.979283+08:00", "timestamp": 1751643562.979283}}}
{"text": "2025-07-04 23:39:23.134 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.163712", "seconds": 6.163712}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:23.134501+08:00", "timestamp": 1751643563.134501}}}
{"text": "2025-07-04 23:39:23.291 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.320308", "seconds": 6.320308}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:23.291097+08:00", "timestamp": 1751643563.291097}}}
{"text": "2025-07-04 23:39:23.638 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.667766", "seconds": 6.667766}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:23.638555+08:00", "timestamp": 1751643563.638555}}}
{"text": "2025-07-04 23:39:23.852 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.882134", "seconds": 6.882134}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:23.852923+08:00", "timestamp": 1751643563.852923}}}
{"text": "2025-07-04 23:39:24.083 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.112623", "seconds": 7.112623}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:24.083412+08:00", "timestamp": 1751643564.083412}}}
{"text": "2025-07-04 23:39:24.312 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.342116", "seconds": 7.342116}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:24.312905+08:00", "timestamp": 1751643564.312905}}}
{"text": "2025-07-04 23:39:24.585 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.614931", "seconds": 7.614931}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:24.585720+08:00", "timestamp": 1751643564.58572}}}
{"text": "2025-07-04 23:39:24.934 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.964130", "seconds": 7.96413}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:24.934919+08:00", "timestamp": 1751643564.934919}}}
{"text": "2025-07-04 23:39:25.223 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:08.253152", "seconds": 8.253152}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:25.223941+08:00", "timestamp": 1751643565.223941}}}
{"text": "2025-07-04 23:39:25.525 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:08.554716", "seconds": 8.554716}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:25.525505+08:00", "timestamp": 1751643565.525505}}}
{"text": "2025-07-04 23:39:25.768 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:08.797674", "seconds": 8.797674}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:25.768463+08:00", "timestamp": 1751643565.768463}}}
{"text": "2025-07-04 23:39:25.880 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:08.909701", "seconds": 8.909701}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:25.880490+08:00", "timestamp": 1751643565.88049}}}
{"text": "2025-07-04 23:39:26.036 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.065221", "seconds": 9.065221}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:26.036010+08:00", "timestamp": 1751643566.03601}}}
{"text": "2025-07-04 23:39:26.251 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.281009", "seconds": 9.281009}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:26.251798+08:00", "timestamp": 1751643566.251798}}}
{"text": "2025-07-04 23:39:26.510 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.539557", "seconds": 9.539557}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:26.510346+08:00", "timestamp": 1751643566.510346}}}
{"text": "2025-07-04 23:39:26.694 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.723221", "seconds": 9.723221}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:26.694010+08:00", "timestamp": 1751643566.69401}}}
{"text": "2025-07-04 23:39:26.908 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.937485", "seconds": 9.937485}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:26.908274+08:00", "timestamp": 1751643566.908274}}}
{"text": "2025-07-04 23:39:27.239 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:10.269163", "seconds": 10.269163}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:27.239952+08:00", "timestamp": 1751643567.239952}}}
{"text": "2025-07-04 23:39:27.350 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:10.379943", "seconds": 10.379943}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:27.350732+08:00", "timestamp": 1751643567.350732}}}
{"text": "2025-07-04 23:39:27.491 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:10.520296", "seconds": 10.520296}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:27.491085+08:00", "timestamp": 1751643567.491085}}}
{"text": "2025-07-04 23:39:27.754 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:10.783531", "seconds": 10.783531}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:27.754320+08:00", "timestamp": 1751643567.75432}}}
{"text": "2025-07-04 23:39:28.101 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:11.131095", "seconds": 11.131095}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:28.101884+08:00", "timestamp": 1751643568.101884}}}
{"text": "2025-07-04 23:39:28.421 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:11.451136", "seconds": 11.451136}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:28.421925+08:00", "timestamp": 1751643568.421925}}}
{"text": "2025-07-04 23:39:28.770 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:11.799237", "seconds": 11.799237}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:28.770026+08:00", "timestamp": 1751643568.770026}}}
{"text": "2025-07-04 23:39:29.132 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.161946", "seconds": 12.161946}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:29.132735+08:00", "timestamp": 1751643569.132735}}}
{"text": "2025-07-04 23:39:29.600 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.629626", "seconds": 12.629626}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:29.600415+08:00", "timestamp": 1751643569.600415}}}
{"text": "2025-07-04 23:39:29.846 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.875437", "seconds": 12.875437}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:29.846226+08:00", "timestamp": 1751643569.846226}}}
{"text": "2025-07-04 23:39:30.105 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.134376", "seconds": 13.134376}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:30.105165+08:00", "timestamp": 1751643570.105165}}}
{"text": "2025-07-04 23:39:30.274 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.304102", "seconds": 13.304102}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:30.274891+08:00", "timestamp": 1751643570.274891}}}
{"text": "2025-07-04 23:39:30.549 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.578766", "seconds": 13.578766}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:30.549555+08:00", "timestamp": 1751643570.549555}}}
{"text": "2025-07-04 23:39:30.704 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.733841", "seconds": 13.733841}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:30.704630+08:00", "timestamp": 1751643570.70463}}}
{"text": "2025-07-04 23:39:30.889 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.919191", "seconds": 13.919191}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:30.889980+08:00", "timestamp": 1751643570.88998}}}
{"text": "2025-07-04 23:39:31.060 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:14.089411", "seconds": 14.089411}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:31.060200+08:00", "timestamp": 1751643571.0602}}}
{"text": "2025-07-04 23:39:31.363 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:14.392357", "seconds": 14.392357}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:31.363146+08:00", "timestamp": 1751643571.363146}}}
{"text": "2025-07-04 23:39:31.533 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:14.562745", "seconds": 14.562745}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:31.533534+08:00", "timestamp": 1751643571.533534}}}
{"text": "2025-07-04 23:39:31.822 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:14.851437", "seconds": 14.851437}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:31.822226+08:00", "timestamp": 1751643571.822226}}}
{"text": "2025-07-04 23:39:31.963 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:14.992894", "seconds": 14.992894}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:31.963683+08:00", "timestamp": 1751643571.963683}}}
{"text": "2025-07-04 23:39:32.237 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:15.267045", "seconds": 15.267045}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:32.237834+08:00", "timestamp": 1751643572.237834}}}
{"text": "2025-07-04 23:39:32.422 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:15.451630", "seconds": 15.45163}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:32.422419+08:00", "timestamp": 1751643572.422419}}}
{"text": "2025-07-04 23:39:32.769 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:15.798741", "seconds": 15.798741}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:32.769530+08:00", "timestamp": 1751643572.76953}}}
{"text": "2025-07-04 23:39:33.119 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:16.148528", "seconds": 16.148528}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:33.119317+08:00", "timestamp": 1751643573.119317}}}
{"text": "2025-07-04 23:39:33.364 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:16.393487", "seconds": 16.393487}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:33.364276+08:00", "timestamp": 1751643573.364276}}}
{"text": "2025-07-04 23:39:33.592 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:16.621575", "seconds": 16.621575}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:33.592364+08:00", "timestamp": 1751643573.592364}}}
{"text": "2025-07-04 23:39:33.791 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:16.821072", "seconds": 16.821072}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:33.791861+08:00", "timestamp": 1751643573.791861}}}
{"text": "2025-07-04 23:39:34.139 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:17.168517", "seconds": 17.168517}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:34.139306+08:00", "timestamp": 1751643574.139306}}}
{"text": "2025-07-04 23:39:34.357 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:17.386539", "seconds": 17.386539}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:34.357328+08:00", "timestamp": 1751643574.357328}}}
{"text": "2025-07-04 23:39:34.586 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:17.616137", "seconds": 17.616137}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:34.586926+08:00", "timestamp": 1751643574.586926}}}
{"text": "2025-07-04 23:39:34.965 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:17.994835", "seconds": 17.994835}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:34.965624+08:00", "timestamp": 1751643574.965624}}}
{"text": "2025-07-04 23:39:35.106 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:18.135299", "seconds": 18.135299}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:35.106088+08:00", "timestamp": 1751643575.106088}}}
{"text": "2025-07-04 23:39:35.261 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:18.290987", "seconds": 18.290987}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:35.261776+08:00", "timestamp": 1751643575.261776}}}
{"text": "2025-07-04 23:39:35.462 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:18.491732", "seconds": 18.491732}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:35.462521+08:00", "timestamp": 1751643575.462521}}}
{"text": "2025-07-04 23:39:35.751 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:18.780277", "seconds": 18.780277}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:35.751066+08:00", "timestamp": 1751643575.751066}}}
{"text": "2025-07-04 23:39:35.937 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:18.966879", "seconds": 18.966879}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:35.937668+08:00", "timestamp": 1751643575.937668}}}
{"text": "2025-07-04 23:39:36.286 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:19.315738", "seconds": 19.315738}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:36.286527+08:00", "timestamp": 1751643576.286527}}}
{"text": "2025-07-04 23:39:36.755 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:19.784771", "seconds": 19.784771}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:36.755560+08:00", "timestamp": 1751643576.75556}}}
{"text": "2025-07-04 23:39:36.973 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:20.002522", "seconds": 20.002522}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:36.973311+08:00", "timestamp": 1751643576.973311}}}
{"text": "2025-07-04 23:39:37.232 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:20.261987", "seconds": 20.261987}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:37.232776+08:00", "timestamp": 1751643577.232776}}}
{"text": "2025-07-04 23:39:37.450 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:20.480098", "seconds": 20.480098}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:37.450887+08:00", "timestamp": 1751643577.450887}}}
{"text": "2025-07-04 23:39:37.591 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:20.620909", "seconds": 20.620909}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:37.591698+08:00", "timestamp": 1751643577.591698}}}
{"text": "2025-07-04 23:39:37.805 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:20.834666", "seconds": 20.834666}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:37.805455+08:00", "timestamp": 1751643577.805455}}}
{"text": "2025-07-04 23:39:38.065 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:21.094555", "seconds": 21.094555}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:38.065344+08:00", "timestamp": 1751643578.065344}}}
{"text": "2025-07-04 23:39:38.265 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:21.294505", "seconds": 21.294505}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:38.265294+08:00", "timestamp": 1751643578.265294}}}
{"text": "2025-07-04 23:39:38.479 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:21.508343", "seconds": 21.508343}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:38.479132+08:00", "timestamp": 1751643578.479132}}}
{"text": "2025-07-04 23:39:38.677 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:21.706442", "seconds": 21.706442}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:38.677231+08:00", "timestamp": 1751643578.677231}}}
{"text": "2025-07-04 23:39:38.860 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:21.889676", "seconds": 21.889676}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:38.860465+08:00", "timestamp": 1751643578.860465}}}
{"text": "2025-07-04 23:39:39.058 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:22.088032", "seconds": 22.088032}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:39.058821+08:00", "timestamp": 1751643579.058821}}}
{"text": "2025-07-04 23:39:39.465 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:22.494902", "seconds": 22.494902}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:39.465691+08:00", "timestamp": 1751643579.465691}}}
{"text": "2025-07-04 23:39:39.665 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:22.695151", "seconds": 22.695151}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:39.665940+08:00", "timestamp": 1751643579.66594}}}
{"text": "2025-07-04 23:39:39.822 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:22.851867", "seconds": 22.851867}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:39.822656+08:00", "timestamp": 1751643579.822656}}}
{"text": "2025-07-04 23:39:40.052 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:23.081773", "seconds": 23.081773}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:40.052562+08:00", "timestamp": 1751643580.052562}}}
{"text": "2025-07-04 23:39:40.194 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:23.223754", "seconds": 23.223754}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:40.194543+08:00", "timestamp": 1751643580.194543}}}
{"text": "2025-07-04 23:39:40.395 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:23.424870", "seconds": 23.42487}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:40.395659+08:00", "timestamp": 1751643580.395659}}}
{"text": "2025-07-04 23:39:40.565 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:23.594804", "seconds": 23.594804}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:40.565593+08:00", "timestamp": 1751643580.565593}}}
{"text": "2025-07-04 23:39:40.691 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:23.720243", "seconds": 23.720243}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:40.691032+08:00", "timestamp": 1751643580.691032}}}
{"text": "2025-07-04 23:39:40.876 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:23.906026", "seconds": 23.906026}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:40.876815+08:00", "timestamp": 1751643580.876815}}}
{"text": "2025-07-04 23:39:41.002 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:24.032150", "seconds": 24.03215}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:41.002939+08:00", "timestamp": 1751643581.002939}}}
{"text": "2025-07-04 23:39:41.232 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:24.262042", "seconds": 24.262042}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:41.232831+08:00", "timestamp": 1751643581.232831}}}
{"text": "2025-07-04 23:39:41.403 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:24.432802", "seconds": 24.432802}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:41.403591+08:00", "timestamp": 1751643581.403591}}}
{"text": "2025-07-04 23:39:41.589 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:24.618729", "seconds": 24.618729}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:41.589518+08:00", "timestamp": 1751643581.589518}}}
{"text": "2025-07-04 23:39:41.804 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:24.833868", "seconds": 24.833868}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:41.804657+08:00", "timestamp": 1751643581.804657}}}
{"text": "2025-07-04 23:39:42.050 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:25.079622", "seconds": 25.079622}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:42.050411+08:00", "timestamp": 1751643582.050411}}}
{"text": "2025-07-04 23:39:42.352 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:25.382140", "seconds": 25.38214}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:42.352929+08:00", "timestamp": 1751643582.352929}}}
{"text": "2025-07-04 23:39:42.718 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:25.747807", "seconds": 25.747807}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:42.718596+08:00", "timestamp": 1751643582.718596}}}
{"text": "2025-07-04 23:39:42.950 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:25.979620", "seconds": 25.97962}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:42.950409+08:00", "timestamp": 1751643582.950409}}}
{"text": "2025-07-04 23:39:43.092 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:26.121920", "seconds": 26.12192}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:43.092709+08:00", "timestamp": 1751643583.092709}}}
{"text": "2025-07-04 23:39:43.381 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:26.411166", "seconds": 26.411166}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:43.381955+08:00", "timestamp": 1751643583.381955}}}
{"text": "2025-07-04 23:39:43.671 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:26.700687", "seconds": 26.700687}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:43.671476+08:00", "timestamp": 1751643583.671476}}}
{"text": "2025-07-04 23:39:43.826 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:26.856077", "seconds": 26.856077}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:43.826866+08:00", "timestamp": 1751643583.826866}}}
{"text": "2025-07-04 23:39:44.056 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:27.085595", "seconds": 27.085595}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:44.056384+08:00", "timestamp": 1751643584.056384}}}
{"text": "2025-07-04 23:39:44.213 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:27.242894", "seconds": 27.242894}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:44.213683+08:00", "timestamp": 1751643584.213683}}}
{"text": "2025-07-04 23:39:44.457 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:27.486427", "seconds": 27.486427}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:44.457216+08:00", "timestamp": 1751643584.457216}}}
{"text": "2025-07-04 23:39:44.583 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:27.612583", "seconds": 27.612583}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:44.583372+08:00", "timestamp": 1751643584.583372}}}
{"text": "2025-07-04 23:39:44.829 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:27.858594", "seconds": 27.858594}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:44.829383+08:00", "timestamp": 1751643584.829383}}}
{"text": "2025-07-04 23:39:44.999 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:28.029122", "seconds": 28.029122}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:44.999911+08:00", "timestamp": 1751643584.999911}}}
{"text": "2025-07-04 23:39:45.155 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:28.184532", "seconds": 28.184532}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:45.155321+08:00", "timestamp": 1751643585.155321}}}
{"text": "2025-07-04 23:39:45.384 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:28.414056", "seconds": 28.414056}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:45.384845+08:00", "timestamp": 1751643585.384845}}}
{"text": "2025-07-04 23:39:45.672 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:28.701921", "seconds": 28.701921}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:45.672710+08:00", "timestamp": 1751643585.67271}}}
{"text": "2025-07-04 23:39:45.903 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:28.932446", "seconds": 28.932446}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:45.903235+08:00", "timestamp": 1751643585.903235}}}
{"text": "2025-07-04 23:39:46.075 | DEBUG    | data_juicer.utils.lazy_loader:466 - Loading torch...\n", "record": {"elapsed": {"repr": "0:00:29.104711", "seconds": 29.104711}, "exception": null, "extra": {}, "file": {"name": "lazy_loader.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/lazy_loader.py"}, "function": "_load", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 466, "message": "Loading torch...", "module": "lazy_loader", "name": "data_juicer.utils.lazy_loader", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:46.075500+08:00", "timestamp": 1751643586.0755}}}
{"text": "2025-07-04 23:39:46.332 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'fork'\n", "record": {"elapsed": {"repr": "0:00:29.361924", "seconds": 29.361924}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'fork'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:46.332713+08:00", "timestamp": 1751643586.332713}}}
{"text": "2025-07-04 23:39:46.406 | DEBUG    | data_juicer.ops.base_op:216 - Op [words_num_filter] running with number of procs:1\n", "record": {"elapsed": {"repr": "0:00:29.435648", "seconds": 29.435648}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [words_num_filter] running with number of procs:1", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:46.406437+08:00", "timestamp": 1751643586.406437}}}
{"text": "2025-07-04 23:39:46.415 | DEBUG    | data_juicer.ops.base_op:216 - Op [words_num_filter] running with number of procs:1\n", "record": {"elapsed": {"repr": "0:00:29.444340", "seconds": 29.44434}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [words_num_filter] running with number of procs:1", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:46.415129+08:00", "timestamp": 1751643586.415129}}}
{"text": "2025-07-04 23:39:46.420 | DEBUG    | data_juicer.ops.base_op:216 - Op [words_num_filter] running with number of procs:1\n", "record": {"elapsed": {"repr": "0:00:29.449836", "seconds": 29.449836}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [words_num_filter] running with number of procs:1", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:46.420625+08:00", "timestamp": 1751643586.420625}}}
{"text": "2025-07-04 23:39:47.010 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'fork'\n", "record": {"elapsed": {"repr": "0:00:30.039367", "seconds": 30.039367}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'fork'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:47.010156+08:00", "timestamp": 1751643587.010156}}}
{"text": "2025-07-04 23:39:47.042 | DEBUG    | data_juicer.ops.base_op:216 - Op [word_repetition_filter] running with number of procs:1\n", "record": {"elapsed": {"repr": "0:00:30.072114", "seconds": 30.072114}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [word_repetition_filter] running with number of procs:1", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:47.042903+08:00", "timestamp": 1751643587.042903}}}
{"text": "2025-07-04 23:39:47.053 | DEBUG    | data_juicer.ops.base_op:216 - Op [word_repetition_filter] running with number of procs:1\n", "record": {"elapsed": {"repr": "0:00:30.082466", "seconds": 30.082466}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [word_repetition_filter] running with number of procs:1", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:47.053255+08:00", "timestamp": 1751643587.053255}}}
