{"text": "2025-07-04 23:39:46.424 | WARNING  | data_juicer.core.tracer:133 - Datasets before and after op [words_num_filter] are all the same. Thus no comparison results would be generated.\n", "record": {"elapsed": {"repr": "0:00:29.453334", "seconds": 29.453334}, "exception": null, "extra": {}, "file": {"name": "tracer.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/core/tracer.py"}, "function": "trace_filter", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 133, "message": "Datasets before and after op [words_num_filter] are all the same. Thus no comparison results would be generated.", "module": "tracer", "name": "data_juicer.core.tracer", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:46.424123+08:00", "timestamp": 1751643586.424123}}}
{"text": "2025-07-04 23:39:47.057 | WARNING  | data_juicer.core.tracer:168 - There are 9 filtered samples before and after op [word_repetition_filter] -- less than expected 10 samples.\n", "record": {"elapsed": {"repr": "0:00:30.087015", "seconds": 30.087015}, "exception": null, "extra": {}, "file": {"name": "tracer.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/core/tracer.py"}, "function": "trace_filter", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 168, "message": "There are 9 filtered samples before and after op [word_repetition_filter] -- less than expected 10 samples.", "module": "tracer", "name": "data_juicer.core.tracer", "process": {"id": 42125, "name": "MainProcess"}, "thread": {"id": 123310188966976, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:39:47.057804+08:00", "timestamp": 1751643587.057804}}}
