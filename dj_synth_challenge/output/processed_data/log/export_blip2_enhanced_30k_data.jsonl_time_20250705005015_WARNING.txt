{"text": "2025-07-05 02:41:57.602 | WARNING  | data_juicer.core.tracer:168 - There are 72 filtered samples before and after op [image_shape_filter] -- less than expected 100 samples.\n", "record": {"elapsed": {"repr": "1:51:43.548293", "seconds": 6703.548293}, "exception": null, "extra": {}, "file": {"name": "tracer.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/core/tracer.py"}, "function": "trace_filter", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 168, "message": "There are 72 filtered samples before and after op [image_shape_filter] -- less than expected 100 samples.", "module": "tracer", "name": "data_juicer.core.tracer", "process": {"id": 74779, "name": "MainProcess"}, "thread": {"id": 133879394370624, "name": "MainThread"}, "time": {"repr": "2025-07-05 02:41:57.602190+08:00", "timestamp": 1751654517.60219}}}
{"text": "2025-07-05 02:41:57.709 | WARNING  | data_juicer.utils.process_utils:64 - The required cuda memory of Op[image_watermark_filter] has not been specified. Please specify the mem_required field in the config file, or you might encounter CUDA out of memory error. You can reference the mem_required field in the config_all.yaml file.\n", "record": {"elapsed": {"repr": "1:51:43.655903", "seconds": 6703.655903}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "calculate_np", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 64, "message": "The required cuda memory of Op[image_watermark_filter] has not been specified. Please specify the mem_required field in the config file, or you might encounter CUDA out of memory error. You can reference the mem_required field in the config_all.yaml file.", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 74779, "name": "MainProcess"}, "thread": {"id": 133879394370624, "name": "MainThread"}, "time": {"repr": "2025-07-05 02:41:57.709800+08:00", "timestamp": 1751654517.7098}}}
{"text": "2025-07-05 02:45:27.518 | WARNING  | data_juicer.utils.process_utils:64 - The required cuda memory of Op[image_watermark_filter] has not been specified. Please specify the mem_required field in the config file, or you might encounter CUDA out of memory error. You can reference the mem_required field in the config_all.yaml file.\n", "record": {"elapsed": {"repr": "1:55:13.464251", "seconds": 6913.464251}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "calculate_np", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 64, "message": "The required cuda memory of Op[image_watermark_filter] has not been specified. Please specify the mem_required field in the config file, or you might encounter CUDA out of memory error. You can reference the mem_required field in the config_all.yaml file.", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 74779, "name": "MainProcess"}, "thread": {"id": 133879394370624, "name": "MainThread"}, "time": {"repr": "2025-07-05 02:45:27.518148+08:00", "timestamp": 1751654727.518148}}}
{"text": "2025-07-05 02:45:32.678 | WARNING  | data_juicer.core.tracer:168 - There are 1 filtered samples before and after op [text_length_filter] -- less than expected 100 samples.\n", "record": {"elapsed": {"repr": "1:55:18.624283", "seconds": 6918.624283}, "exception": null, "extra": {}, "file": {"name": "tracer.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/core/tracer.py"}, "function": "trace_filter", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 168, "message": "There are 1 filtered samples before and after op [text_length_filter] -- less than expected 100 samples.", "module": "tracer", "name": "data_juicer.core.tracer", "process": {"id": 74779, "name": "MainProcess"}, "thread": {"id": 133879394370624, "name": "MainThread"}, "time": {"repr": "2025-07-05 02:45:32.678180+08:00", "timestamp": 1751654732.67818}}}
