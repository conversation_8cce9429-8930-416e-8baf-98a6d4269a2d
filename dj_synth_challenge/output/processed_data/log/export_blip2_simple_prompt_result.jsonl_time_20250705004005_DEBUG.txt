{"text": "2025-07-05 00:40:05.063 | DEBUG    | data_juicer.config.config:35 - Initializing setup from config took 0.01 seconds\n", "record": {"elapsed": {"repr": "0:00:01.912883", "seconds": 1.912883}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Initializing setup from config took 0.01 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:05.063744+08:00", "timestamp": 1751647205.063744}}}
{"text": "2025-07-05 00:40:05.081 | DEBUG    | data_juicer.config.config:35 - Updating operator process took 0.02 seconds\n", "record": {"elapsed": {"repr": "0:00:01.931102", "seconds": 1.931102}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Updating operator process took 0.02 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:05.081963+08:00", "timestamp": 1751647205.081963}}}
{"text": "2025-07-05 00:40:05.086 | DEBUG    | data_juicer.config.config:35 - Total config initialization time took 0.08 seconds\n", "record": {"elapsed": {"repr": "0:00:01.935880", "seconds": 1.93588}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Total config initialization time took 0.08 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:05.086741+08:00", "timestamp": 1751647205.086741}}}
{"text": "2025-07-05 00:40:06.108 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:02.957838", "seconds": 2.957838}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:06.108699+08:00", "timestamp": 1751647206.108699}}}
{"text": "2025-07-05 00:40:06.108 | DEBUG    | data_juicer.utils.lazy_loader:466 - Loading torch...\n", "record": {"elapsed": {"repr": "0:00:02.957936", "seconds": 2.957936}, "exception": null, "extra": {}, "file": {"name": "lazy_loader.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/lazy_loader.py"}, "function": "_load", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 466, "message": "Loading torch...", "module": "lazy_loader", "name": "data_juicer.utils.lazy_loader", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:06.108797+08:00", "timestamp": 1751647206.108797}}}
{"text": "2025-07-05 00:40:06.109 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'forkserver'\n", "record": {"elapsed": {"repr": "0:00:02.958244", "seconds": 2.958244}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'forkserver'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:06.109105+08:00", "timestamp": 1751647206.109105}}}
{"text": "2025-07-05 00:40:06.129 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:02.979045", "seconds": 2.979045}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:06.129906+08:00", "timestamp": 1751647206.129906}}}
{"text": "2025-07-05 00:40:06.151 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:03.000710", "seconds": 3.00071}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:06.151571+08:00", "timestamp": 1751647206.151571}}}
{"text": "2025-07-05 00:40:06.152 | DEBUG    | data_juicer.ops.base_op:216 - Op [image_captioning_mapper] running with number of procs:1\n", "record": {"elapsed": {"repr": "0:00:03.001317", "seconds": 3.001317}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [image_captioning_mapper] running with number of procs:1", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:06.152178+08:00", "timestamp": 1751647206.152178}}}
{"text": "2025-07-05 00:40:06.154 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:03.003312", "seconds": 3.003312}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:06.154173+08:00", "timestamp": 1751647206.154173}}}
{"text": "2025-07-05 00:40:06.155 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:03.004855", "seconds": 3.004855}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:06.155716+08:00", "timestamp": 1751647206.155716}}}
{"text": "2025-07-05 00:40:06.166 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:03.015818", "seconds": 3.015818}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:06.166679+08:00", "timestamp": 1751647206.166679}}}
{"text": "2025-07-05 00:40:06.166 | DEBUG    | data_juicer.utils.model_utils:1110 - functools.partial(<function prepare_huggingface_model at 0x7dfc94b3a710>, pretrained_model_name_or_path='/home/<USER>/.cache/modelscope/hub/models/goldsj/blip2-opt-2.7b', trust_remote_code=False) not found in MODEL_ZOO (MainProcess)\n", "record": {"elapsed": {"repr": "0:00:03.016000", "seconds": 3.016}, "exception": null, "extra": {}, "file": {"name": "model_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/model_utils.py"}, "function": "get_model", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 1110, "message": "functools.partial(<function prepare_huggingface_model at 0x7dfc94b3a710>, pretrained_model_name_or_path='/home/<USER>/.cache/modelscope/hub/models/goldsj/blip2-opt-2.7b', trust_remote_code=False) not found in MODEL_ZOO (MainProcess)", "module": "model_utils", "name": "data_juicer.utils.model_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:06.166861+08:00", "timestamp": 1751647206.166861}}}
{"text": "2025-07-05 00:40:06.167 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:03.016520", "seconds": 3.01652}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:06.167381+08:00", "timestamp": 1751647206.167381}}}
{"text": "2025-07-05 00:40:06.167 | DEBUG    | data_juicer.utils.lazy_loader:466 - Loading transformers...\n", "record": {"elapsed": {"repr": "0:00:03.016633", "seconds": 3.016633}, "exception": null, "extra": {}, "file": {"name": "lazy_loader.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/lazy_loader.py"}, "function": "_load", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 466, "message": "Loading transformers...", "module": "lazy_loader", "name": "data_juicer.utils.lazy_loader", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:06.167494+08:00", "timestamp": 1751647206.167494}}}
{"text": "2025-07-05 00:40:09.043 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:05.892668", "seconds": 5.892668}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:09.043529+08:00", "timestamp": 1751647209.043529}}}
{"text": "2025-07-05 00:40:09.173 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.022397", "seconds": 6.022397}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:09.173258+08:00", "timestamp": 1751647209.173258}}}
{"text": "2025-07-05 00:40:09.226 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.075729", "seconds": 6.075729}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:09.226590+08:00", "timestamp": 1751647209.22659}}}
{"text": "2025-07-05 00:40:09.278 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.127224", "seconds": 6.127224}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:09.278085+08:00", "timestamp": 1751647209.278085}}}
{"text": "2025-07-05 00:40:09.328 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.177668", "seconds": 6.177668}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:09.328529+08:00", "timestamp": 1751647209.328529}}}
{"text": "2025-07-05 00:40:09.378 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.227965", "seconds": 6.227965}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:09.378826+08:00", "timestamp": 1751647209.378826}}}
{"text": "2025-07-05 00:40:09.429 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.278507", "seconds": 6.278507}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:09.429368+08:00", "timestamp": 1751647209.429368}}}
{"text": "2025-07-05 00:40:09.480 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.329714", "seconds": 6.329714}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:09.480575+08:00", "timestamp": 1751647209.480575}}}
{"text": "2025-07-05 00:40:09.721 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.570351", "seconds": 6.570351}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:09.721212+08:00", "timestamp": 1751647209.721212}}}
{"text": "2025-07-05 00:40:10.006 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.855526", "seconds": 6.855526}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:10.006387+08:00", "timestamp": 1751647210.006387}}}
{"text": "2025-07-05 00:40:10.059 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.908144", "seconds": 6.908144}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:10.059005+08:00", "timestamp": 1751647210.059005}}}
{"text": "2025-07-05 00:40:10.110 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:06.959973", "seconds": 6.959973}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:10.110834+08:00", "timestamp": 1751647210.110834}}}
{"text": "2025-07-05 00:40:10.162 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.011427", "seconds": 7.011427}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:10.162288+08:00", "timestamp": 1751647210.162288}}}
{"text": "2025-07-05 00:40:10.213 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.062768", "seconds": 7.062768}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:10.213629+08:00", "timestamp": 1751647210.213629}}}
{"text": "2025-07-05 00:40:10.469 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.318897", "seconds": 7.318897}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:10.469758+08:00", "timestamp": 1751647210.469758}}}
{"text": "2025-07-05 00:40:10.521 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.370822", "seconds": 7.370822}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:10.521683+08:00", "timestamp": 1751647210.521683}}}
{"text": "2025-07-05 00:40:10.573 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.423018", "seconds": 7.423018}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:10.573879+08:00", "timestamp": 1751647210.573879}}}
{"text": "2025-07-05 00:40:10.727 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.576526", "seconds": 7.576526}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:10.727387+08:00", "timestamp": 1751647210.727387}}}
{"text": "2025-07-05 00:40:10.778 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.627837", "seconds": 7.627837}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:10.778698+08:00", "timestamp": 1751647210.778698}}}
{"text": "2025-07-05 00:40:10.829 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.679138", "seconds": 7.679138}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:10.829999+08:00", "timestamp": 1751647210.829999}}}
{"text": "2025-07-05 00:40:10.881 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.730927", "seconds": 7.730927}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:10.881788+08:00", "timestamp": 1751647210.881788}}}
{"text": "2025-07-05 00:40:10.935 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.784752", "seconds": 7.784752}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:10.935613+08:00", "timestamp": 1751647210.935613}}}
{"text": "2025-07-05 00:40:10.988 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.838047", "seconds": 7.838047}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:10.988908+08:00", "timestamp": 1751647210.988908}}}
{"text": "2025-07-05 00:40:11.040 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.890040", "seconds": 7.89004}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:11.040901+08:00", "timestamp": 1751647211.040901}}}
{"text": "2025-07-05 00:40:11.092 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.941599", "seconds": 7.941599}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:11.092460+08:00", "timestamp": 1751647211.09246}}}
{"text": "2025-07-05 00:40:11.145 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:07.994385", "seconds": 7.994385}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:11.145246+08:00", "timestamp": 1751647211.145246}}}
{"text": "2025-07-05 00:40:11.197 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:08.046360", "seconds": 8.04636}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:11.197221+08:00", "timestamp": 1751647211.197221}}}
{"text": "2025-07-05 00:40:11.248 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:08.097898", "seconds": 8.097898}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:11.248759+08:00", "timestamp": 1751647211.248759}}}
{"text": "2025-07-05 00:40:11.416 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:08.265228", "seconds": 8.265228}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:11.416089+08:00", "timestamp": 1751647211.416089}}}
{"text": "2025-07-05 00:40:11.468 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:08.317649", "seconds": 8.317649}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:11.468510+08:00", "timestamp": 1751647211.46851}}}
{"text": "2025-07-05 00:40:11.665 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:08.514698", "seconds": 8.514698}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:11.665559+08:00", "timestamp": 1751647211.665559}}}
{"text": "2025-07-05 00:40:11.717 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:08.566516", "seconds": 8.566516}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:11.717377+08:00", "timestamp": 1751647211.717377}}}
{"text": "2025-07-05 00:40:11.767 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:08.617111", "seconds": 8.617111}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:11.767972+08:00", "timestamp": 1751647211.767972}}}
{"text": "2025-07-05 00:40:11.877 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:08.726517", "seconds": 8.726517}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:11.877378+08:00", "timestamp": 1751647211.877378}}}
{"text": "2025-07-05 00:40:12.162 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.011733", "seconds": 9.011733}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:12.162594+08:00", "timestamp": 1751647212.162594}}}
{"text": "2025-07-05 00:40:12.214 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.063516", "seconds": 9.063516}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:12.214377+08:00", "timestamp": 1751647212.214377}}}
{"text": "2025-07-05 00:40:12.458 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.307244", "seconds": 9.307244}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:12.458105+08:00", "timestamp": 1751647212.458105}}}
{"text": "2025-07-05 00:40:12.510 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.360117", "seconds": 9.360117}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:12.510978+08:00", "timestamp": 1751647212.510978}}}
{"text": "2025-07-05 00:40:12.578 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.427772", "seconds": 9.427772}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:12.578633+08:00", "timestamp": 1751647212.578633}}}
{"text": "2025-07-05 00:40:12.630 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.479780", "seconds": 9.47978}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:12.630641+08:00", "timestamp": 1751647212.630641}}}
{"text": "2025-07-05 00:40:12.682 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.531637", "seconds": 9.531637}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:12.682498+08:00", "timestamp": 1751647212.682498}}}
{"text": "2025-07-05 00:40:12.734 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.584007", "seconds": 9.584007}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:12.734868+08:00", "timestamp": 1751647212.734868}}}
{"text": "2025-07-05 00:40:12.787 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.636225", "seconds": 9.636225}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:12.787086+08:00", "timestamp": 1751647212.787086}}}
{"text": "2025-07-05 00:40:12.838 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.688040", "seconds": 9.68804}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:12.838901+08:00", "timestamp": 1751647212.838901}}}
{"text": "2025-07-05 00:40:12.890 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.739961", "seconds": 9.739961}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:12.890822+08:00", "timestamp": 1751647212.890822}}}
{"text": "2025-07-05 00:40:12.942 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.791452", "seconds": 9.791452}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:12.942313+08:00", "timestamp": 1751647212.942313}}}
{"text": "2025-07-05 00:40:12.995 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.844280", "seconds": 9.84428}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:12.995141+08:00", "timestamp": 1751647212.995141}}}
{"text": "2025-07-05 00:40:13.046 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.895870", "seconds": 9.89587}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:13.046731+08:00", "timestamp": 1751647213.046731}}}
{"text": "2025-07-05 00:40:13.098 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.947763", "seconds": 9.947763}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:13.098624+08:00", "timestamp": 1751647213.098624}}}
{"text": "2025-07-05 00:40:13.150 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:09.999670", "seconds": 9.99967}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:13.150531+08:00", "timestamp": 1751647213.150531}}}
{"text": "2025-07-05 00:40:13.202 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:10.051770", "seconds": 10.05177}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:13.202631+08:00", "timestamp": 1751647213.202631}}}
{"text": "2025-07-05 00:40:13.462 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:10.311636", "seconds": 10.311636}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:13.462497+08:00", "timestamp": 1751647213.462497}}}
{"text": "2025-07-05 00:40:13.515 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:10.364229", "seconds": 10.364229}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:13.515090+08:00", "timestamp": 1751647213.51509}}}
{"text": "2025-07-05 00:40:13.566 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:10.415692", "seconds": 10.415692}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:13.566553+08:00", "timestamp": 1751647213.566553}}}
{"text": "2025-07-05 00:40:13.751 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:10.600344", "seconds": 10.600344}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:13.751205+08:00", "timestamp": 1751647213.751205}}}
{"text": "2025-07-05 00:40:13.803 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:10.652148", "seconds": 10.652148}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:13.803009+08:00", "timestamp": 1751647213.803009}}}
{"text": "2025-07-05 00:40:14.886 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:11.735270", "seconds": 11.73527}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:14.886131+08:00", "timestamp": 1751647214.886131}}}
{"text": "2025-07-05 00:40:14.938 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:11.787869", "seconds": 11.787869}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:14.938730+08:00", "timestamp": 1751647214.93873}}}
{"text": "2025-07-05 00:40:14.991 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:11.840619", "seconds": 11.840619}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:14.991480+08:00", "timestamp": 1751647214.99148}}}
{"text": "2025-07-05 00:40:15.043 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:11.892973", "seconds": 11.892973}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:15.043834+08:00", "timestamp": 1751647215.043834}}}
{"text": "2025-07-05 00:40:15.095 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:11.944857", "seconds": 11.944857}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:15.095718+08:00", "timestamp": 1751647215.095718}}}
{"text": "2025-07-05 00:40:15.147 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:11.996228", "seconds": 11.996228}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:15.147089+08:00", "timestamp": 1751647215.147089}}}
{"text": "2025-07-05 00:40:15.198 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.047390", "seconds": 12.04739}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:15.198251+08:00", "timestamp": 1751647215.198251}}}
{"text": "2025-07-05 00:40:15.249 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.098967", "seconds": 12.098967}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:15.249828+08:00", "timestamp": 1751647215.249828}}}
{"text": "2025-07-05 00:40:15.302 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.151184", "seconds": 12.151184}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:15.302045+08:00", "timestamp": 1751647215.302045}}}
{"text": "2025-07-05 00:40:15.485 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.334532", "seconds": 12.334532}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:15.485393+08:00", "timestamp": 1751647215.485393}}}
{"text": "2025-07-05 00:40:15.536 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.385870", "seconds": 12.38587}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:15.536731+08:00", "timestamp": 1751647215.536731}}}
{"text": "2025-07-05 00:40:15.589 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.438152", "seconds": 12.438152}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:15.589013+08:00", "timestamp": 1751647215.589013}}}
{"text": "2025-07-05 00:40:15.640 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.489994", "seconds": 12.489994}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:15.640855+08:00", "timestamp": 1751647215.640855}}}
{"text": "2025-07-05 00:40:15.692 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.541734", "seconds": 12.541734}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:15.692595+08:00", "timestamp": 1751647215.692595}}}
{"text": "2025-07-05 00:40:15.743 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.592949", "seconds": 12.592949}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:15.743810+08:00", "timestamp": 1751647215.74381}}}
{"text": "2025-07-05 00:40:15.795 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.644489", "seconds": 12.644489}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:15.795350+08:00", "timestamp": 1751647215.79535}}}
{"text": "2025-07-05 00:40:15.846 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.695535", "seconds": 12.695535}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:15.846396+08:00", "timestamp": 1751647215.846396}}}
{"text": "2025-07-05 00:40:16.132 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:12.981207", "seconds": 12.981207}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:16.132068+08:00", "timestamp": 1751647216.132068}}}
{"text": "2025-07-05 00:40:16.184 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.033303", "seconds": 13.033303}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:16.184164+08:00", "timestamp": 1751647216.184164}}}
{"text": "2025-07-05 00:40:16.235 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.084924", "seconds": 13.084924}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:16.235785+08:00", "timestamp": 1751647216.235785}}}
{"text": "2025-07-05 00:40:16.431 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.280653", "seconds": 13.280653}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:16.431514+08:00", "timestamp": 1751647216.431514}}}
{"text": "2025-07-05 00:40:16.525 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.374480", "seconds": 13.37448}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:16.525341+08:00", "timestamp": 1751647216.525341}}}
{"text": "2025-07-05 00:40:16.577 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.426213", "seconds": 13.426213}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:16.577074+08:00", "timestamp": 1751647216.577074}}}
{"text": "2025-07-05 00:40:16.703 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.552281", "seconds": 13.552281}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:16.703142+08:00", "timestamp": 1751647216.703142}}}
{"text": "2025-07-05 00:40:16.754 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.603889", "seconds": 13.603889}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:16.754750+08:00", "timestamp": 1751647216.75475}}}
{"text": "2025-07-05 00:40:16.806 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.655458", "seconds": 13.655458}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:16.806319+08:00", "timestamp": 1751647216.806319}}}
{"text": "2025-07-05 00:40:16.858 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.707479", "seconds": 13.707479}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:16.858340+08:00", "timestamp": 1751647216.85834}}}
{"text": "2025-07-05 00:40:16.910 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:13.759246", "seconds": 13.759246}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:16.910107+08:00", "timestamp": 1751647216.910107}}}
{"text": "2025-07-05 00:40:17.413 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:14.262529", "seconds": 14.262529}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:17.413390+08:00", "timestamp": 1751647217.41339}}}
{"text": "2025-07-05 00:40:17.653 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:14.503011", "seconds": 14.503011}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:17.653872+08:00", "timestamp": 1751647217.653872}}}
{"text": "2025-07-05 00:40:17.910 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:14.759414", "seconds": 14.759414}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:17.910275+08:00", "timestamp": 1751647217.910275}}}
{"text": "2025-07-05 00:40:17.961 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:14.810595", "seconds": 14.810595}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:17.961456+08:00", "timestamp": 1751647217.961456}}}
{"text": "2025-07-05 00:40:18.012 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:14.862045", "seconds": 14.862045}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:18.012906+08:00", "timestamp": 1751647218.012906}}}
{"text": "2025-07-05 00:40:18.064 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:14.913503", "seconds": 14.913503}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:18.064364+08:00", "timestamp": 1751647218.064364}}}
{"text": "2025-07-05 00:40:18.116 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:14.966097", "seconds": 14.966097}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:18.116958+08:00", "timestamp": 1751647218.116958}}}
{"text": "2025-07-05 00:40:18.197 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:15.046456", "seconds": 15.046456}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:18.197317+08:00", "timestamp": 1751647218.197317}}}
{"text": "2025-07-05 00:40:18.248 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:15.097187", "seconds": 15.097187}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:18.248048+08:00", "timestamp": 1751647218.248048}}}
{"text": "2025-07-05 00:40:18.299 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:15.148510", "seconds": 15.14851}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:18.299371+08:00", "timestamp": 1751647218.299371}}}
{"text": "2025-07-05 00:40:18.614 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:15.463709", "seconds": 15.463709}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:18.614570+08:00", "timestamp": 1751647218.61457}}}
{"text": "2025-07-05 00:40:18.665 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:15.514619", "seconds": 15.514619}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:18.665480+08:00", "timestamp": 1751647218.66548}}}
{"text": "2025-07-05 00:40:18.717 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:15.566420", "seconds": 15.56642}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:18.717281+08:00", "timestamp": 1751647218.717281}}}
{"text": "2025-07-05 00:40:18.768 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:15.617633", "seconds": 15.617633}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:18.768494+08:00", "timestamp": 1751647218.768494}}}
{"text": "2025-07-05 00:40:18.819 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:15.668639", "seconds": 15.668639}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:18.819500+08:00", "timestamp": 1751647218.8195}}}
{"text": "2025-07-05 00:40:18.873 | DEBUG    | data_juicer.utils.lazy_loader:466 - Loading torch...\n", "record": {"elapsed": {"repr": "0:00:15.722739", "seconds": 15.722739}, "exception": null, "extra": {}, "file": {"name": "lazy_loader.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/lazy_loader.py"}, "function": "_load", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 466, "message": "Loading torch...", "module": "lazy_loader", "name": "data_juicer.utils.lazy_loader", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:18.873600+08:00", "timestamp": 1751647218.8736}}}
{"text": "2025-07-05 00:40:19.405 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'fork'\n", "record": {"elapsed": {"repr": "0:00:16.254426", "seconds": 16.254426}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'fork'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:19.405287+08:00", "timestamp": 1751647219.405287}}}
{"text": "2025-07-05 00:40:19.463 | DEBUG    | data_juicer.ops.base_op:216 - Op [words_num_filter] running with number of procs:1\n", "record": {"elapsed": {"repr": "0:00:16.313060", "seconds": 16.31306}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [words_num_filter] running with number of procs:1", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:19.463921+08:00", "timestamp": 1751647219.463921}}}
{"text": "2025-07-05 00:40:19.472 | DEBUG    | data_juicer.ops.base_op:216 - Op [words_num_filter] running with number of procs:1\n", "record": {"elapsed": {"repr": "0:00:16.321912", "seconds": 16.321912}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [words_num_filter] running with number of procs:1", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:19.472773+08:00", "timestamp": 1751647219.472773}}}
{"text": "2025-07-05 00:40:19.477 | DEBUG    | data_juicer.ops.base_op:216 - Op [words_num_filter] running with number of procs:1\n", "record": {"elapsed": {"repr": "0:00:16.326748", "seconds": 16.326748}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [words_num_filter] running with number of procs:1", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:19.477609+08:00", "timestamp": 1751647219.477609}}}
{"text": "2025-07-05 00:40:20.054 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'fork'\n", "record": {"elapsed": {"repr": "0:00:16.903667", "seconds": 16.903667}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'fork'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:20.054528+08:00", "timestamp": 1751647220.054528}}}
{"text": "2025-07-05 00:40:20.115 | DEBUG    | data_juicer.ops.base_op:216 - Op [word_repetition_filter] running with number of procs:1\n", "record": {"elapsed": {"repr": "0:00:16.964776", "seconds": 16.964776}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [word_repetition_filter] running with number of procs:1", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:20.115637+08:00", "timestamp": 1751647220.115637}}}
{"text": "2025-07-05 00:40:20.125 | DEBUG    | data_juicer.ops.base_op:216 - Op [word_repetition_filter] running with number of procs:1\n", "record": {"elapsed": {"repr": "0:00:16.975051", "seconds": 16.975051}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [word_repetition_filter] running with number of procs:1", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 73419, "name": "MainProcess"}, "thread": {"id": 138525531485248, "name": "MainThread"}, "time": {"repr": "2025-07-05 00:40:20.125912+08:00", "timestamp": 1751647220.125912}}}
