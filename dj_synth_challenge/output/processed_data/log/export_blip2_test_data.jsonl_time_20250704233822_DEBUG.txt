{"text": "2025-07-04 23:38:22.543 | DEBUG    | data_juicer.config.config:35 - Initializing setup from config took 0.04 seconds\n", "record": {"elapsed": {"repr": "0:00:01.925089", "seconds": 1.925089}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Initializing setup from config took 0.04 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 41460, "name": "MainProcess"}, "thread": {"id": 137942923650112, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:38:22.543432+08:00", "timestamp": 1751643502.543432}}}
{"text": "2025-07-04 23:38:22.562 | DEBUG    | data_juicer.config.config:35 - Updating operator process took 0.02 seconds\n", "record": {"elapsed": {"repr": "0:00:01.943964", "seconds": 1.943964}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Updating operator process took 0.02 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 41460, "name": "MainProcess"}, "thread": {"id": 137942923650112, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:38:22.562307+08:00", "timestamp": 1751643502.562307}}}
{"text": "2025-07-04 23:38:22.567 | DEBUG    | data_juicer.config.config:35 - Total config initialization time took 0.12 seconds\n", "record": {"elapsed": {"repr": "0:00:01.948719", "seconds": 1.948719}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/config/config.py"}, "function": "timing_context", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 35, "message": "Total config initialization time took 0.12 seconds", "module": "config", "name": "data_juicer.config.config", "process": {"id": 41460, "name": "MainProcess"}, "thread": {"id": 137942923650112, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:38:22.567062+08:00", "timestamp": 1751643502.567062}}}
{"text": "2025-07-04 23:38:23.758 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:03.139906", "seconds": 3.139906}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 41460, "name": "MainProcess"}, "thread": {"id": 137942923650112, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:38:23.758249+08:00", "timestamp": 1751643503.758249}}}
{"text": "2025-07-04 23:38:23.758 | DEBUG    | data_juicer.utils.lazy_loader:466 - Loading torch...\n", "record": {"elapsed": {"repr": "0:00:03.140055", "seconds": 3.140055}, "exception": null, "extra": {}, "file": {"name": "lazy_loader.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/lazy_loader.py"}, "function": "_load", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 466, "message": "Loading torch...", "module": "lazy_loader", "name": "data_juicer.utils.lazy_loader", "process": {"id": 41460, "name": "MainProcess"}, "thread": {"id": 137942923650112, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:38:23.758398+08:00", "timestamp": 1751643503.758398}}}
{"text": "2025-07-04 23:38:23.758 | DEBUG    | data_juicer.utils.process_utils:30 - Setting multiprocess start method to 'forkserver'\n", "record": {"elapsed": {"repr": "0:00:03.140645", "seconds": 3.140645}, "exception": null, "extra": {}, "file": {"name": "process_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/process_utils.py"}, "function": "setup_mp", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 30, "message": "Setting multiprocess start method to 'forkserver'", "module": "process_utils", "name": "data_juicer.utils.process_utils", "process": {"id": 41460, "name": "MainProcess"}, "thread": {"id": 137942923650112, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:38:23.758988+08:00", "timestamp": 1751643503.758988}}}
{"text": "2025-07-04 23:38:23.778 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:03.160534", "seconds": 3.160534}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 41460, "name": "MainProcess"}, "thread": {"id": 137942923650112, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:38:23.778877+08:00", "timestamp": 1751643503.778877}}}
{"text": "2025-07-04 23:38:23.795 | DEBUG    | data_juicer.ops.base_op:216 - Op [image_captioning_mapper] running with number of procs:4\n", "record": {"elapsed": {"repr": "0:00:03.176907", "seconds": 3.176907}, "exception": null, "extra": {}, "file": {"name": "base_op.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/ops/base_op.py"}, "function": "runtime_np", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 216, "message": "Op [image_captioning_mapper] running with number of procs:4", "module": "base_op", "name": "data_juicer.ops.base_op", "process": {"id": 41460, "name": "MainProcess"}, "thread": {"id": 137942923650112, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:38:23.795250+08:00", "timestamp": 1751643503.79525}}}
{"text": "2025-07-04 23:38:23.797 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:03.178964", "seconds": 3.178964}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 41460, "name": "MainProcess"}, "thread": {"id": 137942923650112, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:38:23.797307+08:00", "timestamp": 1751643503.797307}}}
{"text": "2025-07-04 23:38:23.799 | DEBUG    | data_juicer.utils.availability_utils:26 - Detected torch version 2.5.1\n", "record": {"elapsed": {"repr": "0:00:03.180915", "seconds": 3.180915}, "exception": null, "extra": {}, "file": {"name": "availability_utils.py", "path": "/home/<USER>/lhp/projects/0625TCSyn/dj_synth_challenge/toolkit/data-juicer/data_juicer/utils/availability_utils.py"}, "function": "_is_package_available", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 26, "message": "Detected torch version 2.5.1", "module": "availability_utils", "name": "data_juicer.utils.availability_utils", "process": {"id": 41460, "name": "MainProcess"}, "thread": {"id": 137942923650112, "name": "MainThread"}, "time": {"repr": "2025-07-04 23:38:23.799258+08:00", "timestamp": 1751643503.799258}}}
