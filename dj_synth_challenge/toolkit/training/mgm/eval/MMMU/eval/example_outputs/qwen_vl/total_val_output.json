{"validation_Accounting_1": "B", "validation_Accounting_2": "D", "validation_Accounting_3": "B", "validation_Accounting_4": "C", "validation_Accounting_5": "A", "validation_Accounting_6": "A", "validation_Accounting_7": "A", "validation_Accounting_8": "A", "validation_Accounting_9": "C", "validation_Accounting_10": "A", "validation_Accounting_11": "A", "validation_Accounting_12": "B", "validation_Accounting_13": "A", "validation_Accounting_14": "B", "validation_Accounting_15": "D", "validation_Accounting_16": "A", "validation_Accounting_17": "D", "validation_Accounting_18": "A", "validation_Accounting_19": "A", "validation_Accounting_20": "C", "validation_Accounting_21": "D", "validation_Accounting_22": "C", "validation_Accounting_23": "A", "validation_Accounting_24": "A", "validation_Accounting_25": "D", "validation_Accounting_26": "D", "validation_Accounting_27": "C", "validation_Accounting_28": "A", "validation_Accounting_29": "B", "validation_Accounting_30": "B", "validation_Agriculture_1": "C", "validation_Agriculture_2": "E", "validation_Agriculture_3": "C", "validation_Agriculture_4": "C", "validation_Agriculture_5": "D", "validation_Agriculture_6": "B", "validation_Agriculture_7": "A", "validation_Agriculture_8": "E", "validation_Agriculture_9": "B", "validation_Agriculture_10": "C", "validation_Agriculture_11": "C", "validation_Agriculture_12": "D", "validation_Agriculture_13": "D", "validation_Agriculture_14": "C", "validation_Agriculture_15": "B", "validation_Agriculture_16": "D", "validation_Agriculture_17": "C", "validation_Agriculture_18": "C", "validation_Agriculture_19": "D", "validation_Agriculture_20": "B", "validation_Agriculture_21": "C", "validation_Agriculture_22": "A", "validation_Agriculture_23": "D", "validation_Agriculture_24": "A", "validation_Agriculture_25": "E", "validation_Agriculture_26": "D", "validation_Agriculture_27": "D", "validation_Agriculture_28": "C", "validation_Agriculture_29": "D", "validation_Agriculture_30": "D", "validation_Architecture_and_Engineering_1": "A", "validation_Architecture_and_Engineering_2": "C", "validation_Architecture_and_Engineering_3": "C", "validation_Architecture_and_Engineering_4": "A", "validation_Architecture_and_Engineering_5": "B", "validation_Architecture_and_Engineering_6": "A", "validation_Architecture_and_Engineering_7": "C", "validation_Architecture_and_Engineering_8": "A", "validation_Architecture_and_Engineering_9": "A", "validation_Architecture_and_Engineering_10": "B", "validation_Architecture_and_Engineering_11": "C", "validation_Architecture_and_Engineering_12": "A", "validation_Architecture_and_Engineering_13": "C", "validation_Architecture_and_Engineering_14": "30.0", "validation_Architecture_and_Engineering_15": "C", "validation_Architecture_and_Engineering_16": "B", "validation_Architecture_and_Engineering_17": "D", "validation_Architecture_and_Engineering_18": "A", "validation_Architecture_and_Engineering_19": "C", "validation_Architecture_and_Engineering_20": "A", "validation_Architecture_and_Engineering_21": "A", "validation_Architecture_and_Engineering_22": "B", "validation_Architecture_and_Engineering_23": "B", "validation_Architecture_and_Engineering_24": "A", "validation_Architecture_and_Engineering_25": "A", "validation_Architecture_and_Engineering_26": "B", "validation_Architecture_and_Engineering_27": "B", "validation_Architecture_and_Engineering_28": "B", "validation_Architecture_and_Engineering_29": "A", "validation_Architecture_and_Engineering_30": "D", "validation_Art_1": "C", "validation_Art_2": "B", "validation_Art_3": "C", "validation_Art_4": "B", "validation_Art_5": "D", "validation_Art_6": "D", "validation_Art_7": "B", "validation_Art_8": "C", "validation_Art_9": "B", "validation_Art_10": "B", "validation_Art_11": "C", "validation_Art_12": "B", "validation_Art_13": "A", "validation_Art_14": "A", "validation_Art_15": "D", "validation_Art_16": "A", "validation_Art_17": "A", "validation_Art_18": "A", "validation_Art_19": "A", "validation_Art_20": "B", "validation_Art_21": "A", "validation_Art_22": "B", "validation_Art_23": "D", "validation_Art_24": "B", "validation_Art_25": "C", "validation_Art_26": "D", "validation_Art_27": "B", "validation_Art_28": "C", "validation_Art_29": "B", "validation_Art_30": "A", "validation_Art_Theory_1": "C", "validation_Art_Theory_2": "C", "validation_Art_Theory_3": "C", "validation_Art_Theory_4": "C", "validation_Art_Theory_5": "C", "validation_Art_Theory_6": "B", "validation_Art_Theory_7": "C", "validation_Art_Theory_8": "A", "validation_Art_Theory_9": "B", "validation_Art_Theory_10": "B", "validation_Art_Theory_11": "C", "validation_Art_Theory_12": "B", "validation_Art_Theory_13": "B", "validation_Art_Theory_14": "B", "validation_Art_Theory_15": "C", "validation_Art_Theory_16": "A", "validation_Art_Theory_17": "C", "validation_Art_Theory_18": "D", "validation_Art_Theory_19": "B", "validation_Art_Theory_20": "D", "validation_Art_Theory_21": "D", "validation_Art_Theory_22": "A", "validation_Art_Theory_23": "D", "validation_Art_Theory_24": "C", "validation_Art_Theory_25": "A", "validation_Art_Theory_26": "D", "validation_Art_Theory_27": "A", "validation_Art_Theory_28": "A", "validation_Art_Theory_29": "A", "validation_Art_Theory_30": "D", "validation_Basic_Medical_Science_1": "D", "validation_Basic_Medical_Science_2": "A", "validation_Basic_Medical_Science_3": "A", "validation_Basic_Medical_Science_4": "A", "validation_Basic_Medical_Science_5": "C", "validation_Basic_Medical_Science_6": "C", "validation_Basic_Medical_Science_7": "A", "validation_Basic_Medical_Science_8": "C", "validation_Basic_Medical_Science_9": "D", "validation_Basic_Medical_Science_10": "labeled as follows: \"h...o...h\"", "validation_Basic_Medical_Science_11": "A", "validation_Basic_Medical_Science_12": "D", "validation_Basic_Medical_Science_13": "C", "validation_Basic_Medical_Science_14": "the production of human growth hormone through transfection of a bacterial cell", "validation_Basic_Medical_Science_15": "A", "validation_Basic_Medical_Science_16": "B", "validation_Basic_Medical_Science_17": "D", "validation_Basic_Medical_Science_18": "A", "validation_Basic_Medical_Science_19": "B", "validation_Basic_Medical_Science_20": "A", "validation_Basic_Medical_Science_21": "C", "validation_Basic_Medical_Science_22": "B", "validation_Basic_Medical_Science_23": "D", "validation_Basic_Medical_Science_24": "A", "validation_Basic_Medical_Science_25": "A", "validation_Basic_Medical_Science_26": "A", "validation_Basic_Medical_Science_27": "C", "validation_Basic_Medical_Science_28": "A", "validation_Basic_Medical_Science_29": "C", "validation_Basic_Medical_Science_30": "C", "validation_Biology_1": "B", "validation_Biology_2": "D", "validation_Biology_3": "B", "validation_Biology_4": "A", "validation_Biology_5": "B", "validation_Biology_6": "C", "validation_Biology_7": "E", "validation_Biology_8": "A", "validation_Biology_9": "D", "validation_Biology_10": "0.0", "validation_Biology_11": "B", "validation_Biology_12": "B", "validation_Biology_13": "E", "validation_Biology_14": "B", "validation_Biology_15": "B", "validation_Biology_16": "B", "validation_Biology_17": "A", "validation_Biology_18": "D", "validation_Biology_19": "C", "validation_Biology_20": "E", "validation_Biology_21": "B", "validation_Biology_22": "D", "validation_Biology_23": "C", "validation_Biology_24": "A", "validation_Biology_25": "A", "validation_Biology_26": "E", "validation_Biology_27": "E", "validation_Biology_28": "A", "validation_Biology_29": "E", "validation_Biology_30": "E", "validation_Chemistry_1": "A", "validation_Chemistry_2": "B", "validation_Chemistry_3": "A", "validation_Chemistry_4": "on the para (side) position relative to the double bond", "validation_Chemistry_5": "C", "validation_Chemistry_6": "C", "validation_Chemistry_7": "A", "validation_Chemistry_8": "A", "validation_Chemistry_9": "A", "validation_Chemistry_10": "C", "validation_Chemistry_11": "B", "validation_Chemistry_12": "B", "validation_Chemistry_13": "25.9365 g of calcium hydroxide was dissolved to make solution a", "validation_Chemistry_14": "B", "validation_Chemistry_15": "A", "validation_Chemistry_16": "D", "validation_Chemistry_17": "A", "validation_Chemistry_18": "D", "validation_Chemistry_19": "located in the middle. because the arrangement of these groups determines the specific orientation of the molecule, isoleucine has one unique optical isomer", "validation_Chemistry_20": "the molecule in the image contains one chiral center", "validation_Chemistry_21": "D", "validation_Chemistry_22": "E", "validation_Chemistry_23": "0.0015 mol", "validation_Chemistry_24": "A", "validation_Chemistry_25": "24.9", "validation_Chemistry_26": "A", "validation_Chemistry_27": "D", "validation_Chemistry_28": "B", "validation_Chemistry_29": "C", "validation_Chemistry_30": "$m_2x_2$", "validation_Clinical_Medicine_1": "C", "validation_Clinical_Medicine_2": "C", "validation_Clinical_Medicine_3": "B", "validation_Clinical_Medicine_4": "C", "validation_Clinical_Medicine_5": "A", "validation_Clinical_Medicine_6": "B", "validation_Clinical_Medicine_7": "C", "validation_Clinical_Medicine_8": "E", "validation_Clinical_Medicine_9": "C", "validation_Clinical_Medicine_10": "D", "validation_Clinical_Medicine_11": "A", "validation_Clinical_Medicine_12": "D", "validation_Clinical_Medicine_13": "D", "validation_Clinical_Medicine_14": "B", "validation_Clinical_Medicine_15": "A", "validation_Clinical_Medicine_16": "E", "validation_Clinical_Medicine_17": "D", "validation_Clinical_Medicine_18": "C", "validation_Clinical_Medicine_19": "C", "validation_Clinical_Medicine_20": "D", "validation_Clinical_Medicine_21": "A", "validation_Clinical_Medicine_22": "D", "validation_Clinical_Medicine_23": "C", "validation_Clinical_Medicine_24": "C", "validation_Clinical_Medicine_25": "C", "validation_Clinical_Medicine_26": "A", "validation_Clinical_Medicine_27": "B", "validation_Clinical_Medicine_28": "C", "validation_Clinical_Medicine_29": "A", "validation_Clinical_Medicine_30": "C", "validation_Computer_Science_1": "D", "validation_Computer_Science_2": "C", "validation_Computer_Science_3": "B", "validation_Computer_Science_4": "B", "validation_Computer_Science_5": "A", "validation_Computer_Science_6": "B", "validation_Computer_Science_7": "C", "validation_Computer_Science_8": "B", "validation_Computer_Science_9": "60.0", "validation_Computer_Science_10": "B", "validation_Computer_Science_11": "D", "validation_Computer_Science_12": "C", "validation_Computer_Science_13": "B", "validation_Computer_Science_14": "D", "validation_Computer_Science_15": "A", "validation_Computer_Science_16": "A", "validation_Computer_Science_17": "A", "validation_Computer_Science_18": "A", "validation_Computer_Science_19": "A", "validation_Computer_Science_20": "A", "validation_Computer_Science_21": "A", "validation_Computer_Science_22": "C", "validation_Computer_Science_23": "288.0", "validation_Computer_Science_24": "C", "validation_Computer_Science_25": "D", "validation_Computer_Science_26": "143.0", "validation_Computer_Science_27": "B", "validation_Computer_Science_28": "A", "validation_Computer_Science_29": "A", "validation_Computer_Science_30": "B", "validation_Design_1": "C", "validation_Design_2": "A", "validation_Design_3": "C", "validation_Design_4": "A", "validation_Design_5": "C", "validation_Design_6": "A", "validation_Design_7": "A", "validation_Design_8": "C", "validation_Design_9": "A", "validation_Design_10": "D", "validation_Design_11": "B", "validation_Design_12": "A", "validation_Design_13": "C", "validation_Design_14": "B", "validation_Design_15": "B", "validation_Design_16": "A", "validation_Design_17": "C", "validation_Design_18": "C", "validation_Design_19": "A", "validation_Design_20": "B", "validation_Design_21": "D", "validation_Design_22": "C", "validation_Design_23": "A", "validation_Design_24": "B", "validation_Design_25": "D", "validation_Design_26": "C", "validation_Design_27": "B", "validation_Design_28": "C", "validation_Design_29": "B", "validation_Design_30": "B", "validation_Diagnostics_and_Laboratory_Medicine_1": "A", "validation_Diagnostics_and_Laboratory_Medicine_2": "D", "validation_Diagnostics_and_Laboratory_Medicine_3": "D", "validation_Diagnostics_and_Laboratory_Medicine_4": "B", "validation_Diagnostics_and_Laboratory_Medicine_5": "C", "validation_Diagnostics_and_Laboratory_Medicine_6": "A", "validation_Diagnostics_and_Laboratory_Medicine_7": "C", "validation_Diagnostics_and_Laboratory_Medicine_8": "A", "validation_Diagnostics_and_Laboratory_Medicine_9": "C", "validation_Diagnostics_and_Laboratory_Medicine_10": "C", "validation_Diagnostics_and_Laboratory_Medicine_11": "A", "validation_Diagnostics_and_Laboratory_Medicine_12": "C", "validation_Diagnostics_and_Laboratory_Medicine_13": "B", "validation_Diagnostics_and_Laboratory_Medicine_14": "C", "validation_Diagnostics_and_Laboratory_Medicine_15": "E", "validation_Diagnostics_and_Laboratory_Medicine_16": "A", "validation_Diagnostics_and_Laboratory_Medicine_17": "A", "validation_Diagnostics_and_Laboratory_Medicine_18": "A", "validation_Diagnostics_and_Laboratory_Medicine_19": "D", "validation_Diagnostics_and_Laboratory_Medicine_20": "E", "validation_Diagnostics_and_Laboratory_Medicine_21": "C", "validation_Diagnostics_and_Laboratory_Medicine_22": "C", "validation_Diagnostics_and_Laboratory_Medicine_23": "C", "validation_Diagnostics_and_Laboratory_Medicine_24": "D", "validation_Diagnostics_and_Laboratory_Medicine_25": "B", "validation_Diagnostics_and_Laboratory_Medicine_26": "B", "validation_Diagnostics_and_Laboratory_Medicine_27": "E", "validation_Diagnostics_and_Laboratory_Medicine_28": "A", "validation_Diagnostics_and_Laboratory_Medicine_29": "D", "validation_Diagnostics_and_Laboratory_Medicine_30": "A", "validation_Economics_1": "A", "validation_Economics_2": "A", "validation_Economics_3": "D", "validation_Economics_4": "B", "validation_Economics_5": "A", "validation_Economics_6": "A", "validation_Economics_7": "A", "validation_Economics_8": "C", "validation_Economics_9": "C", "validation_Economics_10": "B", "validation_Economics_11": "C", "validation_Economics_12": "C", "validation_Economics_13": "A", "validation_Economics_14": "D", "validation_Economics_15": "B", "validation_Economics_16": "A", "validation_Economics_17": "A", "validation_Economics_18": "C", "validation_Economics_19": "C", "validation_Economics_20": "2 bagels", "validation_Economics_21": "C", "validation_Economics_22": "B", "validation_Economics_23": "C", "validation_Economics_24": "A", "validation_Economics_25": "D", "validation_Economics_26": "A", "validation_Economics_27": "A", "validation_Economics_28": "C", "validation_Economics_29": "C", "validation_Economics_30": "B", "validation_Electronics_1": "A", "validation_Electronics_2": "0.6 a", "validation_Electronics_3": "A", "validation_Electronics_4": "17 v$", "validation_Electronics_5": "case", "validation_Electronics_6": "100.0", "validation_Electronics_7": "A", "validation_Electronics_8": "C", "validation_Electronics_9": "A", "validation_Electronics_10": "A", "validation_Electronics_11": "6.0", "validation_Electronics_12": "0 v$", "validation_Electronics_13": "D", "validation_Electronics_14": "B", "validation_Electronics_15": "C", "validation_Electronics_16": "equal to 100v", "validation_Electronics_17": "A", "validation_Electronics_18": "B", "validation_Electronics_19": "A", "validation_Electronics_20": "B", "validation_Electronics_21": "1.0", "validation_Electronics_22": "A", "validation_Electronics_23": "B", "validation_Electronics_24": "14.7", "validation_Electronics_25": "always recommended to consult the manufacturer's specifications and guidelines for safe operation of the rectifier", "validation_Electronics_26": "equal to 10^-6 farad", "validation_Electronics_27": "$120^{\\circ}$", "validation_Electronics_28": "B", "validation_Electronics_29": "12.0", "validation_Electronics_30": "120.0", "validation_Energy_and_Power_1": "C", "validation_Energy_and_Power_2": "A", "validation_Energy_and_Power_3": "B", "validation_Energy_and_Power_4": "A", "validation_Energy_and_Power_5": "A", "validation_Energy_and_Power_6": "A", "validation_Energy_and_Power_7": "C", "validation_Energy_and_Power_8": "A", "validation_Energy_and_Power_9": "B", "validation_Energy_and_Power_10": "B", "validation_Energy_and_Power_11": "C", "validation_Energy_and_Power_12": "A", "validation_Energy_and_Power_13": "A", "validation_Energy_and_Power_14": "C", "validation_Energy_and_Power_15": "C", "validation_Energy_and_Power_16": "C", "validation_Energy_and_Power_17": "C", "validation_Energy_and_Power_18": "C", "validation_Energy_and_Power_19": "B", "validation_Energy_and_Power_20": "A", "validation_Energy_and_Power_21": "B", "validation_Energy_and_Power_22": "A", "validation_Energy_and_Power_23": "C", "validation_Energy_and_Power_24": "A", "validation_Energy_and_Power_25": "C", "validation_Energy_and_Power_26": "C", "validation_Energy_and_Power_27": "A", "validation_Energy_and_Power_28": "A", "validation_Energy_and_Power_29": "B", "validation_Energy_and_Power_30": "C", "validation_Finance_1": "D", "validation_Finance_2": "B", "validation_Finance_3": "A", "validation_Finance_4": "B", "validation_Finance_5": "D", "validation_Finance_6": "B", "validation_Finance_7": "B", "validation_Finance_8": "$1,000", "validation_Finance_9": "D", "validation_Finance_10": "the cost of the watches in dollars would be:", "validation_Finance_11": "B", "validation_Finance_12": "B", "validation_Finance_13": "D", "validation_Finance_14": "A", "validation_Finance_15": "A", "validation_Finance_16": "A", "validation_Finance_17": "24.0", "validation_Finance_18": "A", "validation_Finance_19": "D", "validation_Finance_20": "a discriminatory auction", "validation_Finance_21": "A", "validation_Finance_22": "D", "validation_Finance_23": "13%.", "validation_Finance_24": "the forecast industry earnings retention rate.", "validation_Finance_25": "28.39", "validation_Finance_26": "C", "validation_Finance_27": "A", "validation_Finance_28": "A", "validation_Finance_29": "B", "validation_Finance_30": "0.0", "validation_Geography_1": "B", "validation_Geography_2": "B", "validation_Geography_3": "D", "validation_Geography_4": "a map of an area that includes clearwater beach, downtown, ybor, st. pete, and the bay", "validation_Geography_5": "B", "validation_Geography_6": "A", "validation_Geography_7": "D", "validation_Geography_8": "B", "validation_Geography_9": "A", "validation_Geography_10": "B", "validation_Geography_11": "C", "validation_Geography_12": "D", "validation_Geography_13": "C", "validation_Geography_14": "10.52", "validation_Geography_15": "B", "validation_Geography_16": "C", "validation_Geography_17": "C", "validation_Geography_18": "B", "validation_Geography_19": "A", "validation_Geography_20": "A", "validation_Geography_21": "C", "validation_Geography_22": "two", "validation_Geography_23": "A", "validation_Geography_24": "C", "validation_Geography_25": "C", "validation_Geography_26": "C", "validation_Geography_27": "A", "validation_Geography_28": "B", "validation_Geography_29": "C", "validation_Geography_30": "C", "validation_History_1": "C", "validation_History_2": "B", "validation_History_3": "A", "validation_History_4": "D", "validation_History_5": "B", "validation_History_6": "D", "validation_History_7": "C", "validation_History_8": "C", "validation_History_9": "D", "validation_History_10": "B", "validation_History_11": "D", "validation_History_12": "A", "validation_History_13": "A", "validation_History_14": "C", "validation_History_15": "C", "validation_History_16": "A", "validation_History_17": "D", "validation_History_18": "A", "validation_History_19": "C", "validation_History_20": "B", "validation_History_21": "D", "validation_History_22": "A", "validation_History_23": "B", "validation_History_24": "C", "validation_History_25": "A", "validation_History_26": "A", "validation_History_27": "C", "validation_History_28": "C", "validation_History_29": "D", "validation_History_30": "B", "validation_Literature_1": "C", "validation_Literature_2": "D", "validation_Literature_3": "C", "validation_Literature_4": "D", "validation_Literature_5": "C", "validation_Literature_6": "B", "validation_Literature_7": "A", "validation_Literature_8": "B", "validation_Literature_9": "A", "validation_Literature_10": "C", "validation_Literature_11": "B", "validation_Literature_12": "A", "validation_Literature_13": "D", "validation_Literature_14": "A", "validation_Literature_15": "A", "validation_Literature_16": "A", "validation_Literature_17": "B", "validation_Literature_18": "C", "validation_Literature_19": "B", "validation_Literature_20": "C", "validation_Literature_21": "C", "validation_Literature_22": "A", "validation_Literature_23": "D", "validation_Literature_24": "D", "validation_Literature_25": "B", "validation_Literature_26": "B", "validation_Literature_27": "A", "validation_Literature_28": "D", "validation_Literature_29": "D", "validation_Literature_30": "D", "validation_Manage_1": "A", "validation_Manage_2": "C", "validation_Manage_3": "$216,700", "validation_Manage_4": "D", "validation_Manage_5": "C", "validation_Manage_6": "D", "validation_Manage_7": "C", "validation_Manage_8": "C", "validation_Manage_9": "B", "validation_Manage_10": "C", "validation_Manage_11": "C", "validation_Manage_12": "A", "validation_Manage_13": "B", "validation_Manage_14": "B", "validation_Manage_15": "A", "validation_Manage_16": "A", "validation_Manage_17": "$11.30.", "validation_Manage_18": "B", "validation_Manage_19": "C", "validation_Manage_20": "$0.0256", "validation_Manage_21": "$11.55", "validation_Manage_22": "B", "validation_Manage_23": "if the past trend will continue, the estimated sales in units for 1970 would be 1210 units", "validation_Manage_24": "B", "validation_Manage_25": "0.96", "validation_Manage_26": "C", "validation_Manage_27": "A", "validation_Manage_28": "D", "validation_Manage_29": "A", "validation_Manage_30": "A", "validation_Marketing_1": "A", "validation_Marketing_2": "C", "validation_Marketing_3": "A", "validation_Marketing_4": "C", "validation_Marketing_5": "D", "validation_Marketing_6": "B", "validation_Marketing_7": "B", "validation_Marketing_8": "B", "validation_Marketing_9": "A", "validation_Marketing_10": "C", "validation_Marketing_11": "0.1", "validation_Marketing_12": "A", "validation_Marketing_13": "B", "validation_Marketing_14": "D", "validation_Marketing_15": "A", "validation_Marketing_16": "C", "validation_Marketing_17": "A", "validation_Marketing_18": "C", "validation_Marketing_19": "A", "validation_Marketing_20": "C", "validation_Marketing_21": "B", "validation_Marketing_22": "C", "validation_Marketing_23": "A", "validation_Marketing_24": "B", "validation_Marketing_25": "A", "validation_Marketing_26": "A", "validation_Marketing_27": "A", "validation_Marketing_28": "B", "validation_Marketing_29": "C", "validation_Marketing_30": "C", "validation_Materials_1": "B", "validation_Materials_2": "D", "validation_Materials_3": "C", "validation_Materials_4": "C", "validation_Materials_5": "A", "validation_Materials_6": "D", "validation_Materials_7": "B", "validation_Materials_8": "D", "validation_Materials_9": "A", "validation_Materials_10": "A", "validation_Materials_11": "A", "validation_Materials_12": "A", "validation_Materials_13": "B", "validation_Materials_14": "B", "validation_Materials_15": "C", "validation_Materials_16": "D", "validation_Materials_17": "D", "validation_Materials_18": "C", "validation_Materials_19": "A", "validation_Materials_20": "B", "validation_Materials_21": "A", "validation_Materials_22": "C", "validation_Materials_23": "B", "validation_Materials_24": "C", "validation_Materials_25": "B", "validation_Materials_26": "C", "validation_Materials_27": "C", "validation_Materials_28": "D", "validation_Materials_29": "B", "validation_Materials_30": "B", "validation_Math_1": "B", "validation_Math_2": "B", "validation_Math_3": "B", "validation_Math_4": "A", "validation_Math_5": "B", "validation_Math_6": "C", "validation_Math_7": "D", "validation_Math_8": "B", "validation_Math_9": "A", "validation_Math_10": "A", "validation_Math_11": "A", "validation_Math_12": "C", "validation_Math_13": "A", "validation_Math_14": "A", "validation_Math_15": "the time it takes for the bottom of the ladder to reach the ground.", "validation_Math_16": "B", "validation_Math_17": "A", "validation_Math_18": "B", "validation_Math_19": "C", "validation_Math_20": "A", "validation_Math_21": "B", "validation_Math_22": "C", "validation_Math_23": "C", "validation_Math_24": "A", "validation_Math_25": "C", "validation_Math_26": "B", "validation_Math_27": "C", "validation_Math_28": "A", "validation_Math_29": "A", "validation_Math_30": "B", "validation_Mechanical_Engineering_1": "A", "validation_Mechanical_Engineering_2": "D", "validation_Mechanical_Engineering_3": "D", "validation_Mechanical_Engineering_4": "A", "validation_Mechanical_Engineering_5": "A", "validation_Mechanical_Engineering_6": "B", "validation_Mechanical_Engineering_7": "A", "validation_Mechanical_Engineering_8": "A", "validation_Mechanical_Engineering_9": "C", "validation_Mechanical_Engineering_10": "A", "validation_Mechanical_Engineering_11": "B", "validation_Mechanical_Engineering_12": "D", "validation_Mechanical_Engineering_13": "B", "validation_Mechanical_Engineering_14": "B", "validation_Mechanical_Engineering_15": "B", "validation_Mechanical_Engineering_16": "A", "validation_Mechanical_Engineering_17": "A", "validation_Mechanical_Engineering_18": "A", "validation_Mechanical_Engineering_19": "B", "validation_Mechanical_Engineering_20": "C", "validation_Mechanical_Engineering_21": "A", "validation_Mechanical_Engineering_22": "A", "validation_Mechanical_Engineering_23": "B", "validation_Mechanical_Engineering_24": "B", "validation_Mechanical_Engineering_25": "C", "validation_Mechanical_Engineering_26": "D", "validation_Mechanical_Engineering_27": "B", "validation_Mechanical_Engineering_28": "C", "validation_Mechanical_Engineering_29": "B", "validation_Mechanical_Engineering_30": "B", "validation_Music_1": "D", "validation_Music_2": "B", "validation_Music_3": "D", "validation_Music_4": "A", "validation_Music_5": "B", "validation_Music_6": "B", "validation_Music_7": "B", "validation_Music_8": "A", "validation_Music_9": "C", "validation_Music_10": "B", "validation_Music_11": "A", "validation_Music_12": "B", "validation_Music_13": "D", "validation_Music_14": "C", "validation_Music_15": "C", "validation_Music_16": "D", "validation_Music_17": "A", "validation_Music_18": "A", "validation_Music_19": "B", "validation_Music_20": "D", "validation_Music_21": "D", "validation_Music_22": "A", "validation_Music_23": "C", "validation_Music_24": "C", "validation_Music_25": "C", "validation_Music_26": "B", "validation_Music_27": "C", "validation_Music_28": "D", "validation_Music_29": "C", "validation_Music_30": "B", "validation_Pharmacy_1": "C", "validation_Pharmacy_2": "E", "validation_Pharmacy_3": "can be achieved through the use of an antihistamine medication", "validation_Pharmacy_4": "A", "validation_Pharmacy_5": "A", "validation_Pharmacy_6": "D", "validation_Pharmacy_7": "A", "validation_Pharmacy_8": "A", "validation_Pharmacy_9": "A", "validation_Pharmacy_10": "C", "validation_Pharmacy_11": "A", "validation_Pharmacy_12": "B", "validation_Pharmacy_13": "B", "validation_Pharmacy_14": "B", "validation_Pharmacy_15": "C", "validation_Pharmacy_16": "B", "validation_Pharmacy_17": "D", "validation_Pharmacy_18": "A", "validation_Pharmacy_19": "a regioselective reaction", "validation_Pharmacy_20": "A", "validation_Pharmacy_21": "C", "validation_Pharmacy_22": "C", "validation_Pharmacy_23": "D", "validation_Pharmacy_24": "A", "validation_Pharmacy_25": "C", "validation_Pharmacy_26": " a", "validation_Pharmacy_27": "A", "validation_Pharmacy_28": "D", "validation_Pharmacy_29": "A", "validation_Pharmacy_30": "A", "validation_Physics_1": "C", "validation_Physics_2": "A", "validation_Physics_3": "D", "validation_Physics_4": "A", "validation_Physics_5": "B", "validation_Physics_6": "C", "validation_Physics_7": "B", "validation_Physics_8": "C", "validation_Physics_9": "C", "validation_Physics_10": "B", "validation_Physics_11": "C", "validation_Physics_12": "C", "validation_Physics_13": "A", "validation_Physics_14": "A", "validation_Physics_15": "C", "validation_Physics_16": "B", "validation_Physics_17": "A", "validation_Physics_18": "C", "validation_Physics_19": "D", "validation_Physics_20": "B", "validation_Physics_21": "point a shows the repulsive nature of the strong nuclear force", "validation_Physics_22": "A", "validation_Physics_23": "B", "validation_Physics_24": "C", "validation_Physics_25": "B", "validation_Physics_26": "B", "validation_Physics_27": "B", "validation_Physics_28": "C", "validation_Physics_29": "D", "validation_Physics_30": "B", "validation_Psychology_1": "B", "validation_Psychology_2": "A", "validation_Psychology_3": "E", "validation_Psychology_4": "C", "validation_Psychology_5": "A", "validation_Psychology_6": "B", "validation_Psychology_7": "A", "validation_Psychology_8": "C", "validation_Psychology_9": "E", "validation_Psychology_10": "B", "validation_Psychology_11": "A", "validation_Psychology_12": "C", "validation_Psychology_13": "B", "validation_Psychology_14": "B", "validation_Psychology_15": "E", "validation_Psychology_16": "D", "validation_Psychology_17": "B", "validation_Psychology_18": "C", "validation_Psychology_19": "B", "validation_Psychology_20": "-20.0", "validation_Psychology_21": "C", "validation_Psychology_22": "A", "validation_Psychology_23": "A", "validation_Psychology_24": "B", "validation_Psychology_25": "C", "validation_Psychology_26": "A", "validation_Psychology_27": "A", "validation_Psychology_28": "B", "validation_Psychology_29": "D", "validation_Psychology_30": "B", "validation_Public_Health_1": "C", "validation_Public_Health_2": "B", "validation_Public_Health_3": "A", "validation_Public_Health_4": "D", "validation_Public_Health_5": "C", "validation_Public_Health_6": "B", "validation_Public_Health_7": "A", "validation_Public_Health_8": "B", "validation_Public_Health_9": "B", "validation_Public_Health_10": "B", "validation_Public_Health_11": "D", "validation_Public_Health_12": "A", "validation_Public_Health_13": "A", "validation_Public_Health_14": "D", "validation_Public_Health_15": "C", "validation_Public_Health_16": "B", "validation_Public_Health_17": "C", "validation_Public_Health_18": "B", "validation_Public_Health_19": "A", "validation_Public_Health_20": "B", "validation_Public_Health_21": "D", "validation_Public_Health_22": "A", "validation_Public_Health_23": "D", "validation_Public_Health_24": "C", "validation_Public_Health_25": "B", "validation_Public_Health_26": "C", "validation_Public_Health_27": "C", "validation_Public_Health_28": "D", "validation_Public_Health_29": "A", "validation_Public_Health_30": "A", "validation_Sociology_1": "C", "validation_Sociology_2": "A", "validation_Sociology_3": "B", "validation_Sociology_4": "C", "validation_Sociology_5": "C", "validation_Sociology_6": "B", "validation_Sociology_7": "A", "validation_Sociology_8": "B", "validation_Sociology_9": "C", "validation_Sociology_10": "B", "validation_Sociology_11": "C", "validation_Sociology_12": "B", "validation_Sociology_13": "B", "validation_Sociology_14": "B", "validation_Sociology_15": "C", "validation_Sociology_16": "B", "validation_Sociology_17": "C", "validation_Sociology_18": "A", "validation_Sociology_19": "C", "validation_Sociology_20": "A", "validation_Sociology_21": "A", "validation_Sociology_22": "D", "validation_Sociology_23": "D", "validation_Sociology_24": "D", "validation_Sociology_25": "A", "validation_Sociology_26": "A", "validation_Sociology_27": "B", "validation_Sociology_28": "C", "validation_Sociology_29": "C", "validation_Sociology_30": "E"}