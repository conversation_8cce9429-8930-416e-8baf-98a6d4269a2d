LICENSE
README.md
pyproject.toml
mgm/__init__.py
mgm/constants.py
mgm/conversation.py
mgm/mm_utils.py
mgm/utils.py
mgm.egg-info/PKG-INFO
mgm.egg-info/SOURCES.txt
mgm.egg-info/dependency_links.txt
mgm.egg-info/requires.txt
mgm.egg-info/top_level.txt
mgm/eval/eval_gpt_review.py
mgm/eval/eval_gpt_review_bench.py
mgm/eval/eval_gpt_review_visual.py
mgm/eval/eval_mmbench.py
mgm/eval/eval_pope.py
mgm/eval/eval_science_qa.py
mgm/eval/eval_science_qa_gpt4.py
mgm/eval/eval_science_qa_gpt4_requery.py
mgm/eval/generate_webpage_data_from_table.py
mgm/eval/m4c_evaluator.py
mgm/eval/model_math_vista.py
mgm/eval/model_qa.py
mgm/eval/model_vqa.py
mgm/eval/model_vqa_loader.py
mgm/eval/model_vqa_mmbench.py
mgm/eval/model_vqa_qbench.py
mgm/eval/model_vqa_science.py
mgm/eval/qa_baseline_gpt35.py
mgm/eval/run_llava.py
mgm/eval/summarize_gpt_review.py
mgm/eval/MMMU/eval/convert_to_test.py
mgm/eval/MMMU/eval/eval.py
mgm/eval/MMMU/eval/main_eval_only.py
mgm/eval/MMMU/eval/main_parse_and_eval.py
mgm/eval/MMMU/eval/print_results.py
mgm/eval/MMMU/eval/run_llava.py
mgm/eval/MMMU/eval/utils/data_utils.py
mgm/eval/MMMU/eval/utils/eval_utils.py
mgm/eval/MMMU/eval/utils/model_utils.py
mgm/eval/MathVista/calculate_score.py
mgm/eval/MathVista/extract_answer.py
mgm/eval/MathVista/utilities.py
mgm/eval/MathVista/prompts/ext_ans.py
mgm/model/__init__.py
mgm/model/builder.py
mgm/model/consolidate.py
mgm/model/llava_arch.py
mgm/model/mgm_arch.py
mgm/model/language_model/mgm_gemma.py
mgm/model/language_model/mgm_llama.py
mgm/model/language_model/mgm_mistral.py
mgm/model/language_model/mgm_mixtral.py
mgm/model/multimodal_encoder/builder.py
mgm/model/multimodal_encoder/clip_encoder.py
mgm/model/multimodal_encoder/eva_encoder.py
mgm/model/multimodal_encoder/openclip_encoder.py
mgm/model/multimodal_projector/builder.py
mgm/model/processor/video_processor.py
mgm/serve/__init__.py
mgm/serve/cli.py
mgm/serve/controller.py
mgm/serve/gradio_web_server.py
mgm/serve/model_worker.py
mgm/serve/register_worker.py
mgm/serve/sglang_worker.py
mgm/serve/test_message.py
mgm/train/llama_flash_attn_monkey_patch.py
mgm/train/llama_xformers_attn_monkey_patch.py
mgm/train/llava_trainer.py
mgm/train/train.py
mgm/train/train_mem.py
mgm/train/train_xformers.py
preprocess/check_global_batch_size.py
preprocess/check_sample_number.py