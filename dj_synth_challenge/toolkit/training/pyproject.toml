[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "mgm"
version = "1.0.0"
description = "Mini-Gemini: Mining the Potential of Multi-modality Vision Language Models."
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: Apache Software License",
]
dependencies = [
    "torch==2.0.1", "torchvision==0.15.2", "torchaudio==2.0.2",
    "transformers==4.38.0", "tokenizers==0.15.0", "sentencepiece==0.1.99", "shortuuid",
    "accelerate==0.27.2", "peft==0.4.0", "bitsandbytes==0.41.0",
    "pydantic<2,>=1", "markdown2[all]", "numpy", "scikit-learn==1.2.2",
    "gradio==3.35.2", "gradio_client==0.2.9",
    "requests", "httpx==0.24.0", "uvicorn", "fastapi",
    "einops==0.6.1", "einops-exts==0.0.4", "timm==0.9.16",
    "open_clip_torch", "opencv-python", "python-Levenshtein", "datasets==2.18.0",
    "deepspeed==0.11.1", "ninja", "wandb", "openpyxl"
]

[project.optional-dependencies]
train = ["deepspeed==0.11.1", "ninja", "wandb"]
build = ["build", "twine"]

[project.urls]
"Homepage" = "https://github.com/dvlab-research/MGM"
"Bug Tracker" = "https://github.com/dvlab-research/MGM/issues"

[tool.setuptools.packages.find]
exclude = ["assets*", "benchmark*", "docs", "dist*", "playground*", "scripts*", "tests*", "data*", "model_zoo*", "work_dirs*", "project*"]

[tool.wheel]
exclude = ["assets*", "benchmark*", "docs", "dist*", "playground*", "scripts*", "tests*", "data*", "model_zoo*", "work_dirs*", "project*"]
