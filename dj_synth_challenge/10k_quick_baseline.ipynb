{"cells": [{"cell_type": "markdown", "id": "d3f2cbfc-d4f0-4cf3-8213-ee8216e97cdf", "metadata": {}, "source": ["# Quick Tutorial -- 10k-Sample Baseline\n", "\n", "In this tutorial, we provide a naive baseline trained on only 10k samples quickly. The basic idea is just to recaption them.\n", "\n", "After installation and downloading necessary resources according to the official README file in the competition kit, we can start this baseline tutorial. \n", "\n", "## Recaptioning"]}, {"cell_type": "code", "execution_count": 2, "id": "f32e7d33-a522-46e5-8844-1d9f9707ead6", "metadata": {"ExecutionIndicator": {"show": false}, "execution": {"iopub.execute_input": "2024-07-31T02:45:39.973750Z", "iopub.status.busy": "2024-07-31T02:45:39.973117Z", "iopub.status.idle": "2024-07-31T02:45:39.984413Z", "shell.execute_reply": "2024-07-31T02:45:39.983974Z", "shell.execute_reply.started": "2024-07-31T02:45:39.973727Z"}, "tags": []}, "outputs": [], "source": ["# prepare the recaptioning recipe\n", "recipe = '''\n", "dataset_path: input/pretrain_stage_1_10k/mgm_pretrain_stage_1_10k.jsonl\n", "export_path: output/image_captioning_output/res_10k.jsonl\n", "\n", "process:\n", "  - image_captioning_mapper:\n", "      hf_img2seq: 'Salesforce/blip2-opt-2.7b'  # You can replace this path to a local downloaded HF model\n", "      keep_original_sample: false  # we only need the recaptioned captions\n", "'''\n", "\n", "with open('solution/image_captioning.yaml', 'w') as fout:\n", "    fout.write(recipe)"]}, {"cell_type": "code", "execution_count": 1, "id": "e171d343-85f3-4f64-8731-35f2914f2c28", "metadata": {"execution": {"iopub.execute_input": "2024-07-31T02:55:24.400379Z", "iopub.status.busy": "2024-07-31T02:55:24.400220Z", "iopub.status.idle": "2024-07-31T03:19:00.653374Z", "shell.execute_reply": "2024-07-31T03:19:00.652825Z", "shell.execute_reply.started": "2024-07-31T02:55:24.400362Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-07-31 10:55:28.509581: I tensorflow/core/util/port.cc:113] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n", "2024-07-31 10:55:28.552077: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 AVX512F AVX512_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2024-07-31 10:55:29.265737: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT\n", "2024-07-31 10:55:30.283948: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:30.286172: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:30.313073: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:30.315338: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:30.317562: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:30.319732: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "\u001b[32m2024-07-31 10:55:34\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mdata_juicer.config.config\u001b[0m:\u001b[36m618\u001b[0m - \u001b[1mBack up the input config file [/mnt/workspace/lielin.hyl/dj_synth_test/dj_synth_challenge/solution/image_captioning.yaml] into the work_dir [/mnt/workspace/lielin.hyl/dj_synth_test/dj_synth_challenge/outputs/image_captioning_output]\u001b[0m\n", "\u001b[32m2024-07-31 10:55:34\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mdata_juicer.config.config\u001b[0m:\u001b[36m640\u001b[0m - \u001b[1mConfiguration table: \u001b[0m\n", "╒═════════════════════════╤════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╕\n", "│ key                     │ values                                                                                                             │\n", "╞═════════════════════════╪════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╡\n", "│ config                  │ [Path_fr(solution/image_captioning.yaml, cwd=/mnt/workspace/lielin.hyl/dj_synth_test/dj_synth_challenge)]          │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ hpo_config              │ None                                                                                                               │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ data_probe_algo         │ 'uniform'                                                                                                          │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ data_probe_ratio        │ 1.0                                                                                                                │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ project_name            │ 'hello_world'                                                                                                      │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ executor_type           │ 'default'                                                                                                          │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ dataset_path            │ '/mnt/workspace/lielin.hyl/dj_synth_test/dj_synth_challenge/input/pretrain_stage_1/mgm_pretrain_stage_1_10k.jsonl' │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ export_path             │ '/mnt/workspace/lielin.hyl/dj_synth_test/dj_synth_challenge/outputs/image_captioning_output/res_10k.jsonl'         │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ export_shard_size       │ 0                                                                                                                  │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ export_in_parallel      │ False                                                                                                              │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ keep_stats_in_res_ds    │ False                                                                                                              │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ keep_hashes_in_res_ds   │ False                                                                                                              │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ np                      │ 4                                                                                                                  │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ text_keys               │ 'text'                                                                                                             │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ image_key               │ 'images'                                                                                                           │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ image_special_token     │ '<__dj__image>'                                                                                                    │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ audio_key               │ 'audios'                                                                                                           │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ audio_special_token     │ '<__dj__audio>'                                                                                                    │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ video_key               │ 'videos'                                                                                                           │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ video_special_token     │ '<__dj__video>'                                                                                                    │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ eoc_special_token       │ '<|__dj__eoc|>'                                                                                                    │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ suffixes                │ []                                                                                                                 │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ use_cache               │ True                                                                                                               │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ ds_cache_dir            │ '/root/.cache/huggingface/datasets'                                                                                │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ cache_compress          │ None                                                                                                               │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ use_checkpoint          │ False                                                                                                              │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ temp_dir                │ None                                                                                                               │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ open_tracer             │ False                                                                                                              │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ op_list_to_trace        │ []                                                                                                                 │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ trace_num               │ 10                                                                                                                 │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ op_fusion               │ False                                                                                                              │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ process                 │ [{'image_captioning_mapper': {'accelerator': None,                                                                 │\n", "│                         │                               'audio_key': 'audios',                                                               │\n", "│                         │                               'caption_num': 1,                                                                    │\n", "│                         │                               'cpu_required': 1,                                                                   │\n", "│                         │                               'hf_img2seq': './models/blip2-opt-2.7b',                                             │\n", "│                         │                               'image_key': 'images',                                                               │\n", "│                         │                               'keep_candidate_mode': 'random_any',                                                 │\n", "│                         │                               'keep_original_sample': <PERSON><PERSON><PERSON>,                                                       │\n", "│                         │                               'mem_required': 0,                                                                   │\n", "│                         │                               'num_proc': 4,                                                                       │\n", "│                         │                               'prompt': None,                                                                      │\n", "│                         │                               'prompt_key': None,                                                                  │\n", "│                         │                               'text_key': 'text',                                                                  │\n", "│                         │                               'video_key': 'videos'}}]                                                             │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ percentiles             │ []                                                                                                                 │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ export_original_dataset │ False                                                                                                              │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ save_stats_in_one_file  │ False                                                                                                              │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ ray_address             │ 'auto'                                                                                                             │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ debug                   │ False                                                                                                              │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ work_dir                │ '/mnt/workspace/lielin.hyl/dj_synth_test/dj_synth_challenge/outputs/image_captioning_output'                       │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ timestamp               │ '20240731105532'                                                                                                   │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ dataset_dir             │ '/mnt/workspace/lielin.hyl/dj_synth_test/dj_synth_challenge/input/pretrain_stage_1'                                │\n", "├─────────────────────────┼────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│ add_suffix              │ False                                                                                                              │\n", "╘═════════════════════════╧════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╛\n", "\u001b[32m2024-07-31 10:55:34\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mdata_juicer.core.executor\u001b[0m:\u001b[36m47\u001b[0m - \u001b[1mUsing cache compression method: [None]\u001b[0m\n", "\u001b[32m2024-07-31 10:55:34\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mdata_juicer.core.executor\u001b[0m:\u001b[36m52\u001b[0m - \u001b[1mSetting up data formatter...\u001b[0m\n", "\u001b[32m2024-07-31 10:55:34\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mdata_juicer.core.executor\u001b[0m:\u001b[36m74\u001b[0m - \u001b[1mPreparing exporter...\u001b[0m\n", "\u001b[32m2024-07-31 10:55:34\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mdata_juicer.core.executor\u001b[0m:\u001b[36m151\u001b[0m - \u001b[1mLoading dataset from data formatter...\u001b[0m\n", "\u001b[32m2024-07-31 10:55:35\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mdata_juicer.format.formatter\u001b[0m:\u001b[36m185\u001b[0m - \u001b[1mUnifying the input dataset formats...\u001b[0m\n", "\u001b[32m2024-07-31 10:55:35\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mdata_juicer.format.formatter\u001b[0m:\u001b[36m200\u001b[0m - \u001b[1mThere are 10000 sample(s) in the original dataset.\u001b[0m\n", "\u001b[32m2024-07-31 10:55:35\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mdata_juicer.format.formatter\u001b[0m:\u001b[36m214\u001b[0m - \u001b[1m10000 samples left after filtering empty text.\u001b[0m\n", "\u001b[32m2024-07-31 10:55:35\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mdata_juicer.format.formatter\u001b[0m:\u001b[36m237\u001b[0m - \u001b[1mConverting relative paths in the dataset to their absolute version. (Based on the directory of input dataset file)\u001b[0m\n", "\u001b[32m2024-07-31 10:55:35\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mdata_juicer.format.mixture_formatter\u001b[0m:\u001b[36m137\u001b[0m - \u001b[1msampled 10000 from 10000\u001b[0m\n", "\u001b[32m2024-07-31 10:55:35\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mdata_juicer.format.mixture_formatter\u001b[0m:\u001b[36m143\u001b[0m - \u001b[1mThere are 10000 in final dataset\u001b[0m\n", "\u001b[32m2024-07-31 10:55:35\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mdata_juicer.core.executor\u001b[0m:\u001b[36m157\u001b[0m - \u001b[1mPreparing process operators...\u001b[0m\n", "Loading checkpoint shards: 100%|##########| 2/2 [00:10<00:00,  5.46s/it]\n", "\u001b[32m2024-07-31 10:55:46\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mdata_juicer.core.executor\u001b[0m:\u001b[36m164\u001b[0m - \u001b[1mProcessing data...\u001b[0m\n", "\u001b[32m2024-07-31 10:55:46\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36mdata_juicer.utils.process_utils\u001b[0m:\u001b[36m70\u001b[0m - \u001b[33m\u001b[1mThe required cuda memory of Op[image_captioning_mapper] has not been specified. Please specify the mem_required field in the config file, or you might encounter CUDA out of memory error. You can reference the mem_required field in the config_all.yaml file.\u001b[0m\n", "image_captioning_mapper_process (num_proc=4):   0%|          | 0/10000 [00:00<?, ? examples/s]2024-07-31 10:55:50.644234: I tensorflow/core/util/port.cc:113] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n", "2024-07-31 10:55:50.659618: I tensorflow/core/util/port.cc:113] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n", "2024-07-31 10:55:50.673869: I tensorflow/core/util/port.cc:113] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n", "2024-07-31 10:55:50.685130: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 AVX512F AVX512_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2024-07-31 10:55:50.699908: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 AVX512F AVX512_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2024-07-31 10:55:50.716550: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 AVX512F AVX512_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2024-07-31 10:55:50.730978: I tensorflow/core/util/port.cc:113] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n", "2024-07-31 10:55:50.774333: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 AVX512F AVX512_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2024-07-31 10:55:50.870935: I tensorflow/core/util/port.cc:113] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n", "2024-07-31 10:55:50.916116: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 AVX512F AVX512_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2024-07-31 10:55:51.609879: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT\n", "2024-07-31 10:55:51.612656: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT\n", "2024-07-31 10:55:51.785613: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT\n", "2024-07-31 10:55:51.865557: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT\n", "2024-07-31 10:55:51.998638: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT\n", "2024-07-31 10:55:52.331871: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.335220: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.337715: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.339907: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.359966: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.362200: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.362390: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.364421: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.364623: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.366736: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.366843: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.369019: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.537275: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.542718: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.575030: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.581676: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.585700: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.587895: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.614126: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.616338: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.643082: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.645430: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.647711: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.652592: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.740755: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.742968: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.770161: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.772434: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.774653: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "2024-07-31 10:55:52.776824: I external/local_xla/xla/stream_executor/cuda/cuda_executor.cc:998] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355\n", "Loading checkpoint shards: 100%|██████████████████| 2/2 [00:14<00:00,  7.04s/it]\n", "Loading checkpoint shards: 100%|██████████████████| 2/2 [00:14<00:00,  7.06s/it]\n", "Loading checkpoint shards: 100%|██████████████████| 2/2 [00:14<00:00,  7.01s/it]\n", "Loading checkpoint shards: 100%|██████████████████| 2/2 [00:14<00:00,  7.06s/it]\n", "image_captioning_mapper_process (num_proc=4): 100%|##########| 10000/10000 [23:11<00:00,  7.18 examples/s]\n", "\u001b[32m2024-07-31 11:18:59\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mdata_juicer.core.data\u001b[0m:\u001b[36m193\u001b[0m - \u001b[1mOP [image_captioning_mapper] Done in 1392.556s. Left 10000 samples.\u001b[0m\n", "\u001b[32m2024-07-31 11:18:59\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mdata_juicer.core.executor\u001b[0m:\u001b[36m171\u001b[0m - \u001b[1mAll OPs are done in 1392.556s.\u001b[0m\n", "\u001b[32m2024-07-31 11:18:59\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mdata_juicer.core.executor\u001b[0m:\u001b[36m174\u001b[0m - \u001b[1mExporting dataset to disk...\u001b[0m\n", "\u001b[32m2024-07-31 11:18:59\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mdata_juicer.core.exporter\u001b[0m:\u001b[36m140\u001b[0m - \u001b[1mExport dataset into a single file...\u001b[0m\n", "Creating json from Arrow format: 100%|##########| 10/10 [00:00<00:00, 229.41ba/s]\n"]}], "source": ["# recaption the 10k data\n", "!dj-process --config solution/image_captioning.yaml"]}, {"cell_type": "markdown", "id": "1c61fa75-a544-407c-ae3c-6ce8050c8815", "metadata": {}, "source": ["# Train and Inference\n", "\n", "For this quick baseline, we also prepare a 12k finetuning subset to align the scaling of pretraining dataset in the competition kit.\n", "\n", "Now you can replace the PRETRAIN_DATASET parameter in the training script `toolkit/train_mgm_2b_stage_1_10k_baseline.sh`, rename the exp name as you expect.\n", "\n", "For example,\n", "```shell\n", "############################################################################\n", "########################### Editable Part Begins ###########################\n", "############################################################################\n", "...\n", "\n", "# exp meta information\n", "EXP_NAME=image_recaption  # change to a prefered experiment name\n", "PRETRAIN_DATASET=/path/to/the/output/image_captioning_output/res_10k.jsonl  # change it to the path to the generated dataset\n", "PRETRAIN_DATASET_IMAGE_PATH=../input/pretrain_stage_1_10k  # change it to the path to store the generated images (if any and if it's different from the original image dir)\n", "\n", "...\n", "\n", "############################################################################\n", "############################ Editable Part Ends ############################\n", "############################################################################\n", "```\n", "\n", "Then, you just need to start the training:\n", "```shell\n", "cd toolkit\n", "bash train_mgm_2b_stage_1_10k_baseline.sh\n", "```"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}